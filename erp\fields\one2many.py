"""
One2Many relational field implementation
"""
from .base import RelationalField, FieldValidationError


class One2Many(RelationalField):
    """One-to-many relationship field (inverse of Many2One)"""

    def __init__(self, comodel_name, inverse_name, **kwargs):
        """
        Initialize One2Many field

        Args:
            comodel_name: Name of the related model
            inverse_name: Name of the Many2One field in the related model
                         that points back to this model
        """
        super().__init__(comodel_name, **kwargs)
        self.inverse_name = inverse_name
        # One2Many fields are not stored in database directly
        self.store = False

    def get_sql_type(self):
        """One2Many fields don't have SQL representation"""
        return None

    def _validate_value(self, value):
        """Validate One2Many field value"""
        if value is None:
            return None

        # Value can be:
        # 1. A list of IDs
        # 2. A list of model instances
        # 3. A RecordSet
        # 4. Command tuples for operations

        if isinstance(value, list):
            # Handle command tuples or list of records/IDs
            validated_commands = []
            for item in value:
                if isinstance(item, tuple) and len(item) >= 2:
                    # Command tuple: (command, id, values)
                    command = item[0]
                    if command in (0, 1, 2, 3, 4, 5, 6):  # Valid command codes
                        validated_commands.append(item)
                    else:
                        raise FieldValidationError(f"Invalid command code: {command}")
                elif isinstance(item, str):
                    # ID string
                    validated_commands.append((4, item, 0))  # Link command
                elif isinstance(item, int):
                    # ID integer
                    validated_commands.append((4, str(item), 0))  # Link command
                elif hasattr(item, 'id'):
                    # Model instance
                    validated_commands.append((4, item.id, 0))  # Link command
                else:
                    raise FieldValidationError(f"Invalid item type in One2Many list: {type(item)}")
            return validated_commands

        elif hasattr(value, '_records'):
            # RecordSet - convert to link commands
            return [(4, record.id, 0) for record in value._records]

        else:
            raise FieldValidationError(f"Invalid value type for One2Many field: {type(value)}")

    def convert_to_cache(self, value):
        """Convert value for caching"""
        if value is None:
            return []
        # Store as list of IDs
        if isinstance(value, list):
            ids = []
            for item in value:
                if isinstance(item, tuple) and len(item) >= 2:
                    command, record_id = item[0], item[1]
                    if command in (4, 1):  # Link or update
                        ids.append(record_id)
                    # Skip other commands for cache
                else:
                    ids.append(str(item))
            return ids
        return []

    def convert_to_record(self, value):
        """Convert cached value back to record value"""
        if not value:
            return []
        # TODO: Implement proper RecordSet creation when model registry is available
        return value
