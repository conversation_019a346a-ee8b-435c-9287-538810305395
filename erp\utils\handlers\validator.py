"""
Request validation utilities for ERP system
Contains validators for incoming requests and data
"""
from typing import Any, List, Optional
from fastapi import HTTPException


class RequestValidator:
    """Validator for incoming requests"""
    
    @staticmethod
    def validate_model_name(model_name: str) -> bool:
        """Validate model name format"""
        if not model_name:
            return False
        
        # Model names should contain only letters, numbers, dots, and underscores
        import re
        pattern = r'^[a-zA-Z][a-zA-Z0-9._]*$'
        return bool(re.match(pattern, model_name))
    
    @staticmethod
    def validate_record_ids(record_ids: Any) -> List[str]:
        """Validate and normalize record IDs"""
        if not record_ids:
            raise HTTPException(status_code=400, detail="Record IDs are required")
        
        if isinstance(record_ids, str):
            record_ids = [record_ids]
        elif not isinstance(record_ids, list):
            raise HTTPException(status_code=400, detail="Record IDs must be a string or list of strings")
        
        # Validate each ID
        validated_ids = []
        for record_id in record_ids:
            if not isinstance(record_id, str):
                record_id = str(record_id)
            
            if not record_id.strip():
                raise HTTPException(status_code=400, detail="Empty record ID not allowed")
            
            validated_ids.append(record_id.strip())
        
        return validated_ids
    
    @staticmethod
    def validate_domain(domain: Any) -> List:
        """Validate domain format"""
        if domain is None:
            return []
        
        if not isinstance(domain, list):
            raise HTTPException(status_code=400, detail="Domain must be a list")
        
        # Basic domain validation - each item should be a tuple/list with 3 elements
        for item in domain:
            if not isinstance(item, (list, tuple)) or len(item) != 3:
                raise HTTPException(
                    status_code=400, 
                    detail="Domain items must be tuples/lists with 3 elements: (field, operator, value)"
                )
        
        return domain
    
    @staticmethod
    def validate_fields(fields: Any) -> Optional[List[str]]:
        """Validate fields list"""
        if fields is None:
            return None
        
        if isinstance(fields, str):
            fields = [fields]
        elif not isinstance(fields, list):
            raise HTTPException(status_code=400, detail="Fields must be a string or list of strings")
        
        # Validate each field name
        validated_fields = []
        for field in fields:
            if not isinstance(field, str):
                raise HTTPException(status_code=400, detail="Field names must be strings")
            
            if not field.strip():
                raise HTTPException(status_code=400, detail="Empty field name not allowed")
            
            validated_fields.append(field.strip())
        
        return validated_fields
    
    @staticmethod
    def validate_pagination(limit: Any, offset: Any) -> tuple:
        """Validate pagination parameters"""
        if limit is not None:
            try:
                limit = int(limit)
                if limit < 0:
                    raise HTTPException(status_code=400, detail="Limit must be non-negative")
                if limit > 10000:  # Reasonable upper limit
                    raise HTTPException(status_code=400, detail="Limit too large (max 10000)")
            except (ValueError, TypeError):
                raise HTTPException(status_code=400, detail="Limit must be an integer")
        
        if offset is not None:
            try:
                offset = int(offset)
                if offset < 0:
                    raise HTTPException(status_code=400, detail="Offset must be non-negative")
            except (ValueError, TypeError):
                raise HTTPException(status_code=400, detail="Offset must be an integer")
        else:
            offset = 0
        
        return limit, offset

    @staticmethod
    def validate_search_operator(operator: str) -> str:
        """Validate search operator"""
        valid_operators = ['=', '!=', 'like', 'ilike', 'in', 'not in', '>', '<', '>=', '<=']
        
        if operator not in valid_operators:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid operator '{operator}'. Valid operators: {', '.join(valid_operators)}"
            )
        
        return operator

    @staticmethod
    def validate_method_name(method_name: str) -> str:
        """Validate method name format"""
        if not method_name:
            raise HTTPException(status_code=400, detail="Method name is required")
        
        # Method names should be valid Python identifiers
        if not method_name.isidentifier():
            raise HTTPException(status_code=400, detail="Invalid method name format")
        
        # Prevent access to private methods
        if method_name.startswith('_'):
            raise HTTPException(status_code=400, detail="Access to private methods not allowed")
        
        return method_name

    @staticmethod
    def validate_create_data(data: Any) -> dict:
        """Validate data for create operations"""
        if not data:
            raise HTTPException(status_code=400, detail="Data is required for create operation")
        
        if not isinstance(data, dict):
            raise HTTPException(status_code=400, detail="Data must be a dictionary")
        
        # Check for empty data
        if not data:
            raise HTTPException(status_code=400, detail="Data cannot be empty")
        
        return data

    @staticmethod
    def validate_write_data(data: Any) -> dict:
        """Validate data for write operations"""
        if not data:
            raise HTTPException(status_code=400, detail="Data is required for write operation")
        
        if not isinstance(data, dict):
            raise HTTPException(status_code=400, detail="Data must be a dictionary")
        
        return data

    @staticmethod
    def validate_copy_defaults(defaults: Any) -> dict:
        """Validate defaults for copy operations"""
        if defaults is None:
            return {}
        
        if not isinstance(defaults, dict):
            raise HTTPException(status_code=400, detail="Copy defaults must be a dictionary")
        
        return defaults

    @staticmethod
    def validate_context(context: Any) -> dict:
        """Validate context parameter"""
        if context is None:
            return {}
        
        if not isinstance(context, dict):
            raise HTTPException(status_code=400, detail="Context must be a dictionary")
        
        return context

    @staticmethod
    def validate_args_kwargs(args: Any, kwargs: Any) -> tuple:
        """Validate args and kwargs for method calls"""
        if args is None:
            args = []
        elif not isinstance(args, list):
            raise HTTPException(status_code=400, detail="Args must be a list")
        
        if kwargs is None:
            kwargs = {}
        elif not isinstance(kwargs, dict):
            raise HTTPException(status_code=400, detail="Kwargs must be a dictionary")
        
        return args, kwargs

    @staticmethod
    def validate_field_attributes(attributes: Any) -> Optional[List[str]]:
        """Validate field attributes list"""
        if attributes is None:
            return None
        
        if isinstance(attributes, str):
            attributes = [attributes]
        elif not isinstance(attributes, list):
            raise HTTPException(status_code=400, detail="Attributes must be a string or list of strings")
        
        # Validate each attribute name
        validated_attributes = []
        for attr in attributes:
            if not isinstance(attr, str):
                raise HTTPException(status_code=400, detail="Attribute names must be strings")
            
            if not attr.strip():
                raise HTTPException(status_code=400, detail="Empty attribute name not allowed")
            
            validated_attributes.append(attr.strip())
        
        return validated_attributes

    @staticmethod
    def validate_name_search_params(name: str, operator: str, limit: int) -> tuple:
        """Validate parameters for name_search"""
        # Name can be empty string
        if name is None:
            name = ''
        elif not isinstance(name, str):
            name = str(name)
        
        # Validate operator
        operator = RequestValidator.validate_search_operator(operator)
        
        # Validate limit
        if limit is not None:
            try:
                limit = int(limit)
                if limit < 0:
                    raise HTTPException(status_code=400, detail="Limit must be non-negative")
                if limit > 1000:  # Reasonable upper limit for name search
                    raise HTTPException(status_code=400, detail="Limit too large (max 1000)")
            except (ValueError, TypeError):
                raise HTTPException(status_code=400, detail="Limit must be an integer")
        else:
            limit = 100  # Default limit
        
        return name, operator, limit

    @staticmethod
    def validate_request_data(data: dict, required_fields: List[str] = None, 
                            optional_fields: List[str] = None) -> dict:
        """
        Validate request data against required and optional fields
        
        Args:
            data: Request data to validate
            required_fields: List of required field names
            optional_fields: List of optional field names
            
        Returns:
            Validated data dictionary
        """
        if not isinstance(data, dict):
            raise HTTPException(status_code=400, detail="Request data must be a dictionary")
        
        if required_fields:
            missing_fields = []
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Missing required fields: {', '.join(missing_fields)}"
                )
        
        # If optional_fields is provided, filter out unknown fields
        if optional_fields is not None:
            allowed_fields = set(required_fields or []) | set(optional_fields)
            filtered_data = {}
            
            for key, value in data.items():
                if key in allowed_fields:
                    filtered_data[key] = value
            
            return filtered_data
        
        return data
