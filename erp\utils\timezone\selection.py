"""
Timezone selection utilities
Contains utilities for generating timezone selection lists for UI components
"""
from typing import List, Tuple, Dict, Optional
from datetime import datetime
from .core import (
    get_common_timezones, validate_timezone, get_timezone_offset,
    ZONEINFO_AVAILABLE
)

# Import pytz if zoneinfo is not available
if not ZONEINFO_AVAILABLE:
    import pytz
else:
    from zoneinfo import ZoneInfo


def get_timezone_selection() -> List[Tuple[str, str]]:
    """
    Get timezone selection list for use in Selection fields
    
    Returns:
        List of (timezone_name, display_name) tuples
    """
    timezones = get_common_timezones()
    selection = []
    
    for tz_name in timezones:
        # Create a more user-friendly display name
        display_name = tz_name.replace('_', ' ')
        
        # Add UTC offset information for better UX
        try:
            if ZONEINFO_AVAILABLE:
                tz = ZoneInfo(tz_name)
                now = datetime.now(tz)
            else:
                tz = pytz.timezone(tz_name)
                now = datetime.now(tz)
            
            offset = now.strftime('%z')
            if offset:
                # Format offset as +/-HH:MM
                offset_formatted = f"{offset[:3]}:{offset[3:]}"
                display_name = f"{display_name} (UTC{offset_formatted})"
        except Exception:
            # If we can't get offset info, just use the name
            pass
        
        selection.append((tz_name, display_name))
    
    return selection


def get_timezone_selection_lambda():
    """
    Lambda function that returns timezone selection for use in Selection fields
    This can be used as: Selection(selection=get_timezone_selection_lambda())
    """
    return get_timezone_selection()


def get_grouped_timezone_selection() -> Dict[str, List[Tuple[str, str]]]:
    """
    Get timezone selection grouped by region
    
    Returns:
        Dictionary mapping region names to lists of (timezone_name, display_name) tuples
    """
    timezones = get_common_timezones()
    grouped = {}
    
    for tz_name in timezones:
        # Determine region from timezone name
        region = _get_timezone_region(tz_name)
        
        if region not in grouped:
            grouped[region] = []
        
        # Create display name with offset
        display_name = _create_timezone_display_name(tz_name)
        grouped[region].append((tz_name, display_name))
    
    # Sort each group by display name
    for region in grouped:
        grouped[region].sort(key=lambda x: x[1])
    
    return grouped


def get_timezone_selection_for_country(country_code: str) -> List[Tuple[str, str]]:
    """
    Get timezone selection for a specific country
    
    Args:
        country_code: ISO country code (e.g., 'US', 'GB', 'DE')
        
    Returns:
        List of (timezone_name, display_name) tuples for the country
    """
    # Country to timezone mapping (simplified)
    country_timezones = {
        'US': ['US/Eastern', 'US/Central', 'US/Mountain', 'US/Pacific', 'US/Alaska', 'US/Hawaii'],
        'GB': ['Europe/London'],
        'DE': ['Europe/Berlin'],
        'FR': ['Europe/Paris'],
        'IT': ['Europe/Rome'],
        'ES': ['Europe/Madrid'],
        'JP': ['Asia/Tokyo'],
        'CN': ['Asia/Shanghai'],
        'IN': ['Asia/Kolkata'],
        'AU': ['Australia/Sydney', 'Australia/Melbourne', 'Australia/Perth'],
        'CA': ['America/Toronto', 'America/Vancouver', 'America/Edmonton'],
        'BR': ['America/Sao_Paulo'],
        'MX': ['America/Mexico_City'],
        'AE': ['Asia/Dubai'],
        'EG': ['Africa/Cairo'],
        'ZA': ['Africa/Johannesburg'],
    }
    
    timezones = country_timezones.get(country_code.upper(), [])
    selection = []
    
    for tz_name in timezones:
        if validate_timezone(tz_name):
            display_name = _create_timezone_display_name(tz_name)
            selection.append((tz_name, display_name))
    
    return selection


def get_popular_timezone_selection() -> List[Tuple[str, str]]:
    """
    Get a curated list of popular timezones for quick selection
    
    Returns:
        List of (timezone_name, display_name) tuples for popular timezones
    """
    popular_timezones = [
        'UTC',
        'US/Eastern',
        'US/Central',
        'US/Mountain',
        'US/Pacific',
        'Europe/London',
        'Europe/Paris',
        'Europe/Berlin',
        'Asia/Tokyo',
        'Asia/Shanghai',
        'Asia/Kolkata',
        'Australia/Sydney',
    ]
    
    selection = []
    for tz_name in popular_timezones:
        if validate_timezone(tz_name):
            display_name = _create_timezone_display_name(tz_name)
            selection.append((tz_name, display_name))
    
    return selection


def search_timezones(query: str, limit: int = 10) -> List[Tuple[str, str]]:
    """
    Search for timezones matching a query string
    
    Args:
        query: Search query (city, country, or timezone name)
        limit: Maximum number of results to return
        
    Returns:
        List of (timezone_name, display_name) tuples matching the query
    """
    if not query:
        return []
    
    query_lower = query.lower()
    all_timezones = get_common_timezones()
    matches = []
    
    for tz_name in all_timezones:
        # Check if query matches timezone name or parts of it
        if (query_lower in tz_name.lower() or 
            query_lower in tz_name.replace('_', ' ').lower() or
            query_lower in tz_name.split('/')[-1].lower()):
            
            display_name = _create_timezone_display_name(tz_name)
            matches.append((tz_name, display_name))
            
            if len(matches) >= limit:
                break
    
    return matches


def get_timezone_selection_with_categories() -> Dict[str, List[Tuple[str, str]]]:
    """
    Get timezone selection organized by categories
    
    Returns:
        Dictionary with categorized timezone selections
    """
    return {
        'Popular': get_popular_timezone_selection(),
        'Americas': _get_timezones_by_prefix(['America/', 'US/', 'Canada/']),
        'Europe': _get_timezones_by_prefix(['Europe/']),
        'Asia': _get_timezones_by_prefix(['Asia/']),
        'Africa': _get_timezones_by_prefix(['Africa/']),
        'Australia': _get_timezones_by_prefix(['Australia/']),
        'Pacific': _get_timezones_by_prefix(['Pacific/']),
        'UTC': [('UTC', 'UTC (Coordinated Universal Time)')]
    }


def _get_timezone_region(tz_name: str) -> str:
    """
    Extract region from timezone name
    
    Args:
        tz_name: Timezone name
        
    Returns:
        Region name
    """
    if '/' in tz_name:
        return tz_name.split('/')[0]
    elif tz_name.startswith('US/'):
        return 'US'
    elif tz_name == 'UTC':
        return 'UTC'
    else:
        return 'Other'


def _create_timezone_display_name(tz_name: str) -> str:
    """
    Create a user-friendly display name for a timezone
    
    Args:
        tz_name: Timezone name
        
    Returns:
        Formatted display name with offset
    """
    display_name = tz_name.replace('_', ' ')
    
    # Add UTC offset information
    offset = get_timezone_offset(tz_name)
    if offset:
        display_name = f"{display_name} (UTC{offset})"
    
    return display_name


def _get_timezones_by_prefix(prefixes: List[str]) -> List[Tuple[str, str]]:
    """
    Get timezones that start with any of the given prefixes
    
    Args:
        prefixes: List of timezone name prefixes
        
    Returns:
        List of (timezone_name, display_name) tuples
    """
    all_timezones = get_common_timezones()
    matches = []
    
    for tz_name in all_timezones:
        for prefix in prefixes:
            if tz_name.startswith(prefix):
                display_name = _create_timezone_display_name(tz_name)
                matches.append((tz_name, display_name))
                break
    
    # Sort by display name
    matches.sort(key=lambda x: x[1])
    return matches


def validate_timezone_selection(selection: List[Tuple[str, str]]) -> List[Tuple[str, str]]:
    """
    Validate and filter a timezone selection list
    
    Args:
        selection: List of (timezone_name, display_name) tuples
        
    Returns:
        Filtered list with only valid timezones
    """
    valid_selection = []
    
    for tz_name, display_name in selection:
        if validate_timezone(tz_name):
            valid_selection.append((tz_name, display_name))
    
    return valid_selection


def get_timezone_selection_for_ui(include_popular: bool = True, 
                                 include_grouped: bool = False,
                                 max_items: Optional[int] = None) -> List[Tuple[str, str]]:
    """
    Get timezone selection optimized for UI components
    
    Args:
        include_popular: Whether to prioritize popular timezones
        include_grouped: Whether to include regional grouping
        max_items: Maximum number of items to return
        
    Returns:
        List of (timezone_name, display_name) tuples optimized for UI
    """
    if include_popular:
        selection = get_popular_timezone_selection()
        
        # Add separator
        selection.append(('', '--- All Timezones ---'))
        
        # Add remaining timezones
        all_timezones = get_timezone_selection()
        popular_names = {tz[0] for tz in get_popular_timezone_selection()}
        
        for tz_name, display_name in all_timezones:
            if tz_name not in popular_names:
                selection.append((tz_name, display_name))
    else:
        selection = get_timezone_selection()
    
    if max_items and len(selection) > max_items:
        selection = selection[:max_items]
    
    return selection
