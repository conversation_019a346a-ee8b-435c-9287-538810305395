"""
Core HTTP functionality
Contains essential HTTP components like JSON-RPC, route handling, and exceptions
"""

from .jsonrpc import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JsonRpcError, JsonRpcRequest, JsonRpcResponse
from .exceptions import HTTPError, RouteError, AuthenticationError, AuthorizationError, ValidationError
from .html_errors import HTMLErrorResponse
from .error_detection import (
    RouteTypeDetector, ErrorResponseFactory,
    detect_route_type_from_request, create_appropriate_error_response
)

__all__ = [
    'JsonRpcHandler', 'JsonRpcError', 'JsonRpcRequest', 'JsonRpcResponse',
    'HTTPError', 'RouteError', 'AuthenticationError', 'AuthorizationError', 'ValidationError',
    'HTMLErrorResponse',
    'RouteTypeDetector', 'ErrorResponseFactory',
    'detect_route_type_from_request', 'create_appropriate_error_response'
]
