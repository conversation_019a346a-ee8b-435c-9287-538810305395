"""
Cluster networking configuration
"""
import os
import socket
from typing import Dict, Any

from ..config import config
from ..logging import get_logger


class ClusterNetworking:
    """Handles cluster networking configuration"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.cluster_info = {}
    
    def setup_cluster_networking(self, cluster_id: str, node_id: str) -> Dict[str, Any]:
        """Setup cluster networking configuration"""
        try:
            # Get local IP address
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            
            server_config = config.server_config.config
            
            self.cluster_info.update({
                'node_id': node_id,
                'cluster_id': cluster_id,
                'local_ip': local_ip,
                'bind_host': server_config['host'],
                'bind_port': server_config['port'],
                'hostname': socket.gethostname(),
                'pid': os.getpid(),
                'cluster_nodes': self._get_cluster_nodes_config()
            })
            
            self.logger.info(f"🌐 Cluster node configured: {local_ip}:{server_config['port']}")
            return self.cluster_info.copy()
            
        except Exception as e:
            self.logger.warning(f"Failed to setup cluster networking: {e}")
            return {}
    
    def _get_cluster_nodes_config(self) -> str:
        """Get cluster nodes configuration"""
        cluster_nodes = config.cluster_config.nodes
        
        if cluster_nodes.lower() == "auto":
            # Auto-detect cluster nodes (for now, return "auto")
            # In a real implementation, this could discover other nodes
            # via network discovery, service registry, etc.
            return "auto"
        else:
            # Validate that it's a number
            try:
                nodes_count = int(cluster_nodes)
                if nodes_count <= 0:
                    self.logger.warning(f"Invalid cluster_nodes value: {cluster_nodes}, using auto")
                    return "auto"
                return str(nodes_count)
            except ValueError:
                self.logger.warning(f"Invalid cluster_nodes value: {cluster_nodes}, using auto")
                return "auto"
