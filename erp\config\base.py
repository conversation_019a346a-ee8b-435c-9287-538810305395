"""
Base configuration management
"""
import os
import configparser
from typing import Dict, Any, Optional, List


class Config:
    """Base configuration manager"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config = configparser.ConfigParser()
        self.config_file = config_file or self._find_config_file()
        self._load_config()
        
        # Initialize sub-configurations
        self._database_config = None
        self._logging_config = None
        self._server_config = None
        self._cluster_config = None
    
    def _find_config_file(self) -> str:
        """Find configuration file"""
        possible_paths = [
            'erp.conf',  # Root directory first (new priority)
            'config/erp.conf',
            '/etc/erp/erp.conf',
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # Return default path if none found
        return 'erp.conf'
    
    def _load_config(self):
        """Load configuration from file"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file)
        else:
            # Set default values
            self._set_defaults()
    
    def _set_defaults(self):
        """Set default configuration values"""
        self.config.add_section('options')
        self.config.set('options', 'http_port', '8069')
        self.config.set('options', 'http_interface', '127.0.0.1')
        self.config.set('options', 'db_host', 'localhost')
        self.config.set('options', 'db_port', '5432')
        self.config.set('options', 'db_user', 'erp')
        self.config.set('options', 'db_password', 'erp')
        self.config.set('options', 'db_name', 'erp_db')
        self.config.set('options', 'list_db', 'True')
        self.config.set('options', 'db_filter', '.*')
        self.config.set('options', 'addons_path', 'addons')
        self.config.set('options', 'log_level', 'info')
        self.config.set('options', 'log_file', 'logs/erp.log')
        self.config.set('options', 'log_console_enabled', 'true')
        self.config.set('options', 'log_console_level', 'info')
        self.config.set('options', 'log_colored_console', 'true')
        self.config.set('options', 'log_file_enabled', 'true')
        self.config.set('options', 'log_file_level', 'debug')
        self.config.set('options', 'log_file_rotating', 'true')
        self.config.set('options', 'log_file_max_bytes', '10485760')
        self.config.set('options', 'log_file_backup_count', '5')
        self.config.set('options', 'log_handlers', 'console,file')
        self.config.set('options', 'admin_passwd', 'admin')
        self.config.set('options', 'db_pool_min_size', '10')
        self.config.set('options', 'db_pool_max_size', '20')
        self.config.set('options', 'server_type', 'asgi')
        self.config.set('options', 'cors_origins', '*')
        self.config.set('options', 'cluster_mode', 'false')
        self.config.set('options', 'cluster_nodes', 'auto')
    
    def get(self, section: str, option: str, fallback: Any = None) -> Any:
        """Get configuration value"""
        try:
            return self.config.get(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return fallback
    
    def getint(self, section: str, option: str, fallback: int = 0) -> int:
        """Get integer configuration value"""
        try:
            return self.config.getint(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback

    def getfloat(self, section: str, option: str, fallback: float = 0.0) -> float:
        """Get float configuration value"""
        try:
            return self.config.getfloat(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback
    
    def getboolean(self, section: str, option: str, fallback: bool = False) -> bool:
        """Get boolean configuration value"""
        try:
            return self.config.getboolean(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback
    
    def set(self, section: str, option: str, value: str):
        """Set configuration value"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, option, value)
    
    def save(self):
        """Save configuration to file"""
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        with open(self.config_file, 'w') as f:
            self.config.write(f)

    def sections(self):
        """Get all configuration sections"""
        return self.config.sections()

    def options(self, section: str):
        """Get all options in a section"""
        try:
            return self.config.options(section)
        except configparser.NoSectionError:
            return []

    def has_section(self, section: str) -> bool:
        """Check if section exists"""
        return self.config.has_section(section)
    
    def _parse_list(self, section: str, option: str, default: Optional[List[str]] = None) -> List[str]:
        """Parse comma-separated list from config"""
        value = self.get(section, option, '')
        if not value:
            return default or []
        return [item.strip() for item in value.split(',') if item.strip()]
    
    @property
    def addons_paths(self) -> List[str]:
        """Get all addons paths as a list"""
        paths_str = self.get('options', 'addons_path', 'addons')
        # Split by comma and strip whitespace
        paths = [path.strip() for path in paths_str.split(',') if path.strip()]
        return paths if paths else ['addons']

    @property
    def addons_path(self) -> str:
        """Get the first addons path (for backward compatibility)"""
        paths = self.addons_paths
        return paths[0] if paths else 'addons'

    @property
    def system_routes(self) -> List[str]:
        """Get system routes that should skip database middleware"""
        return self._parse_list('options', 'system_routes', [
            '/openapi.json', '/docs', '/redoc', '/favicon.ico'
        ])
    
    @property
    def is_development_mode(self) -> bool:
        """Check if running in development mode"""
        # Check environment variable first
        import os
        env_dev = os.getenv('ERP_DEV_MODE', '').lower() in ('true', '1', 'yes', 'on')
        if env_dev:
            return True
        
        # Check config file
        return self.getboolean('options', 'dev_mode', False)
    
    # Lazy-loaded sub-configurations
    @property
    def db_config(self) -> 'DatabaseConfig':
        """Get database configuration"""
        if self._database_config is None:
            from .database import DatabaseConfig
            self._database_config = DatabaseConfig(self)
        return self._database_config
    
    @property
    def logging_config(self) -> 'LoggingConfig':
        """Get logging configuration"""
        if self._logging_config is None:
            from .logging import LoggingConfig
            self._logging_config = LoggingConfig(self)
        return self._logging_config
    
    @property
    def server_config(self) -> 'ServerConfig':
        """Get server configuration"""
        if self._server_config is None:
            from .server import ServerConfig
            self._server_config = ServerConfig(self)
        return self._server_config
    
    @property
    def cluster_config(self) -> 'ClusterConfig':
        """Get cluster configuration"""
        if self._cluster_config is None:
            from .cluster import ClusterConfig
            self._cluster_config = ClusterConfig(self)
        return self._cluster_config
    
    # Backward compatibility properties
    @property
    def cors_origins(self) -> list:
        """Get CORS origins configuration"""
        return self.server_config.cors_origins
    
    @property
    def list_db(self) -> bool:
        """Check if database listing is enabled"""
        return self.db_config.list_db
    
    @property
    def db_pool_config(self) -> Dict[str, Any]:
        """Get database pool configuration"""
        return self.db_config.pool_config
    
    @property
    def is_single_db_mode(self) -> bool:
        """Check if running in single database mode"""
        return self.db_config.is_single_db_mode
    
    @property
    def is_multi_db_mode(self) -> bool:
        """Check if running in multi-database mode"""
        return self.db_config.is_multi_db_mode
    
    @property
    def db_filter(self) -> Optional[str]:
        """Get database filter pattern for multi-database mode"""
        return self.db_config.filter
    
    @property
    def cluster_mode(self) -> bool:
        """Check if cluster mode is enabled"""
        return self.cluster_config.enabled
    
    @property
    def cluster_nodes(self) -> str:
        """Get cluster nodes configuration"""
        return self.cluster_config.nodes
    
    def get_default_database(self) -> Optional[str]:
        """Get default database name based on mode"""
        return self.db_config.get_default_database()
