[options]
# =============================================================================
# ERP SYSTEM CONFIGURATION
# =============================================================================
# This is the consolidated configuration file for the ERP system.
# Only one configuration file is needed in the root directory.

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
http_port = 8069
http_interface = 127.0.0.1
server_type = asgi

# =============================================================================
# CLUSTER CONFIGURATION
# =============================================================================
# Enable cluster mode for distributed ERP deployment
# When enabled, the system will setup cluster-specific configurations
# Default: false (single server mode)
cluster_mode = false

# Number of cluster nodes to expect in the cluster
# - Set to a specific number (e.g., 3) to configure for a fixed cluster size
# - Set to "auto" to automatically detect the number of nodes
# - Only used when cluster_mode = true
# Default: auto (automatic detection)
cluster_nodes = auto

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp

# =============================================================================
# DATABASE MODE CONFIGURATION
# =============================================================================
# The system supports two modes:
#
# 1. SINGLE DATABASE MODE (Mono DB):
#    - Set db_name to a specific database name
#    - The system will only connect to this one database
#    - Example: db_name = erp_production
#
# 2. MULTI-DATABASE MODE:
#    - Leave db_name empty or comment it out
#    - The system can work with multiple databases
#    - Use db_filter to control which databases are accessible
#
# Current setting: MULTI-DATABASE MODE (for testing database list)
# db_name = erp_db

# =============================================================================
# MULTI-DATABASE SUPPORT
# =============================================================================
# These settings only apply when db_name is not set (multi-database mode)
#
# list_db: Allow listing of available databases
list_db = True

# db_filter: Filter which databases are accessible
# - If null/empty: ALL databases are loaded and accessible
# - If not null: Only databases matching the pattern are accessible
# - Uses regex patterns (e.g., ^erp_.* matches databases starting with "erp_")
#
# Examples:
# db_filter = ^erp_.*          # Only databases starting with "erp_"
# db_filter = ^(erp|test)_.*   # Databases starting with "erp_" or "test_"
# db_filter =                  # Empty = load ALL databases
db_filter =

# =============================================================================
# CONNECTION POOLING
# =============================================================================
db_pool_min_size = 10
db_pool_max_size = 20

# =============================================================================
# ADDONS CONFIGURATION
# =============================================================================
# Multiple addon paths can be specified, comma-separated
# The system will search for addons in all specified paths
# If the same addon exists in multiple paths, the first one found is used
#
# Examples:
# addons_path = addons                                    # Single path
# addons_path = addons,custom_addons                      # Two paths
# addons_path = addons,/opt/erp/addons,~/my_addons       # Multiple paths
addons_path = addons

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# The ERP system provides a comprehensive logging framework with multiple
# output destinations, intelligent filtering, and performance monitoring.
# All logging is automatically formatted with colors and proper spacing.

# -----------------------------------------------------------------------------
# BASIC LOGGING SETTINGS
# -----------------------------------------------------------------------------
# log_level: Global minimum log level (debug, info, warning, error, critical)
#           Controls which messages are processed by the logging system
log_level = debug

# log_file: Path to the main log file (relative to ERP root or absolute path)
#          Directory will be created automatically if it doesn't exist
log_file = logs/erp.log

# -----------------------------------------------------------------------------
# CONSOLE LOGGING (Terminal Output)
# -----------------------------------------------------------------------------
# log_console_enabled: Enable/disable console output (true/false)
#                      Recommended: true for development, false for production daemons
log_console_enabled = true

# log_console_level: Minimum level for console messages (debug, info, warning, error, critical)
#                   Typically higher than file logging to reduce noise
log_console_level = info

# log_colored_console: Enable colored output in terminal (true/false)
#                     Automatically disabled on non-TTY terminals
#                     Colors: DEBUG=cyan, INFO=green, WARNING=yellow, ERROR=red, CRITICAL=magenta
log_colored_console = true

# -----------------------------------------------------------------------------
# FILE LOGGING (Persistent Storage)
# -----------------------------------------------------------------------------
# log_file_enabled: Enable/disable file logging (true/false)
#                  Recommended: always true for production systems
log_file_enabled = true

# log_file_level: Minimum level for file messages (debug, info, warning, error, critical)
#                Typically 'debug' to capture all information for troubleshooting
log_file_level = debug

# log_file_rotating: Enable log file rotation to prevent huge files (true/false)
#                   When enabled, creates backup files when size limit is reached
log_file_rotating = true

# log_file_max_bytes: Maximum size per log file in bytes before rotation
#                    Default: 10MB (10485760 bytes), adjust based on disk space
log_file_max_bytes = 10485760

# log_file_backup_count: Number of backup files to keep during rotation
#                       Total disk usage = max_bytes * (backup_count + 1)
log_file_backup_count = 5

# log_file_json: Output file logs in JSON format for log aggregation tools (true/false)
#               Enable for ELK stack, Splunk, or other log analysis systems
log_file_json = false

# -----------------------------------------------------------------------------
# TIMED LOG ROTATION (Alternative to Size-Based)
# -----------------------------------------------------------------------------
# log_file_timed_rotating: Use time-based rotation instead of size-based (true/false)
#                         Useful for compliance requirements or regular archiving
log_file_timed_rotating = false

# log_file_when: Rotation interval (midnight, H, D, W0-W6, or S)
#               midnight = daily at midnight, H = hourly, D = daily, W0 = weekly on Monday
log_file_when = midnight

# log_file_interval: Number of intervals between rotations
#                   Example: when=H, interval=6 means every 6 hours
log_file_interval = 1

# -----------------------------------------------------------------------------
# STRUCTURED LOGGING (JSON Output)
# -----------------------------------------------------------------------------
# log_json_enabled: Enable JSON structured logging for better parsing (true/false)
#                  Useful for log aggregation, monitoring, and analysis tools
log_json_enabled = false

# log_json_include_extra: Include additional context fields in JSON logs (true/false)
#                        Adds user_id, database, request_id, and custom fields
log_json_include_extra = true

# -----------------------------------------------------------------------------
# DATABASE LOGGING (Critical Events)
# -----------------------------------------------------------------------------
# log_database_enabled: Store critical log events in database (true/false)
#                       Useful for audit trails and real-time monitoring
log_database_enabled = false

# log_database_level: Minimum level for database storage (warning, error, critical)
#                    Only store important events to avoid database bloat
log_database_level = warning

# log_database_table: Database table name for log storage
#                    Table will be created automatically with proper indexes
log_database_table = system_logs

# log_database_buffer_size: Number of log entries to buffer before database write
#                          Higher values improve performance but risk data loss
log_database_buffer_size = 100

# -----------------------------------------------------------------------------
# PERFORMANCE MONITORING
# -----------------------------------------------------------------------------
# log_performance_enabled: Enable automatic performance logging (true/false)
#                          Tracks slow operations, database queries, and API calls
log_performance_enabled = true

# log_performance_threshold: Minimum duration in seconds to log performance events
#                           Only operations slower than this will be logged
log_performance_threshold = 1.0

# -----------------------------------------------------------------------------
# SECURITY LOGGING
# -----------------------------------------------------------------------------
# log_security_enabled: Enable security event logging (true/false)
#                       Tracks login attempts, permission errors, and suspicious activity
log_security_enabled = true

# log_security_level: Minimum level for security events (info, warning, error)
#                    Recommended: warning to capture failed attempts and errors
log_security_level = warning

# -----------------------------------------------------------------------------
# LOG FILTERING AND RATE LIMITING
# -----------------------------------------------------------------------------
# log_rate_limit_enabled: Prevent log flooding by limiting message rate (true/false)
#                        Useful to prevent disk space issues during errors
log_rate_limit_enabled = false

# log_rate_limit_max_records: Maximum log messages per time window
#                            Excess messages will be dropped with a summary
log_rate_limit_max_records = 100

# log_rate_limit_time_window: Time window in seconds for rate limiting
#                            Example: 100 messages per 60 seconds maximum
log_rate_limit_time_window = 60

# log_duplicate_filter_enabled: Reduce duplicate log messages (true/false)
#                              Useful to reduce noise from repeated errors
log_duplicate_filter_enabled = false

# log_duplicate_max_count: Maximum identical messages before suppression
#                         After this count, duplicates show "X more suppressed"
log_duplicate_max_count = 5

# log_duplicate_time_window: Time window in seconds for duplicate detection
#                           Duplicates are only detected within this timeframe
log_duplicate_time_window = 60

# log_module_filter_enabled: Filter logs by module/package names (true/false)
#                           Useful to focus on specific components or exclude noisy modules
log_module_filter_enabled = false

# log_module_filter_include: Comma-separated list of module patterns to include
#                           Example: erp.database.*,addons.accounting.*
#                           Empty means include all (if exclude list is used)
log_module_filter_include =

# log_module_filter_exclude: Comma-separated list of module patterns to exclude
#                           Example: *.migrations.*,urllib3.*
#                           Useful to reduce noise from third-party libraries
log_module_filter_exclude =

# -----------------------------------------------------------------------------
# OUTPUT DESTINATIONS
# -----------------------------------------------------------------------------
# log_handlers: Comma-separated list of active log handlers
#              Available: console, file, database, memory
#              Example: console,file for development, file,database for production
log_handlers = console,file

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
admin_passwd = admin

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# CORS origins (comma-separated)
# Use * for all origins, or specify specific origins
# Example: cors_origins = http://localhost:3000,https://myapp.com
cors_origins = *

# =============================================================================
# SYSTEM ROUTES CONFIGURATION
# =============================================================================
# System routes that should skip database middleware (comma-separated)
# These are FastAPI internal routes and other system endpoints that don't need database context
# Examples: /openapi.json, /docs, /redoc, /favicon.ico
# Note: Routes from the system route registry are automatically included
system_routes = /openapi.json,/docs,/redoc,/favicon.ico

# =============================================================================
# SQL LOGGING CONFIGURATION
# =============================================================================
# Enable SQL query logging at debug level (requires log_level = debug)
# When enabled, all SQL queries will be logged with execution time and parameters
# This is useful for debugging database performance and query optimization
# WARNING: This can generate a lot of log output and may impact performance
sql_logging_enabled = true

# Maximum length of SQL queries to log (longer queries will be truncated)
sql_logging_max_query_length = 500

# Maximum length of SQL parameters to log (longer params will be truncated)
sql_logging_max_params_length = 200