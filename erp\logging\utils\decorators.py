"""
Logging Decorators - Decorators for automatic logging
"""
import functools
import logging
import time
import asyncio
from typing import Callable, Optional, Any, Dict
from .utils import log_structured


def log_method_calls(logger: Optional[logging.Logger] = None, 
                    level: int = logging.DEBUG,
                    include_args: bool = False,
                    include_result: bool = False):
    """Decorator to log method calls"""
    
    def decorator(func: Callable) -> Callable:
        nonlocal logger
        if logger is None:
            logger = logging.getLogger(func.__module__)
            
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            func_name = f"{func.__qualname__}"
            
            # Log method entry
            log_data = {'operation': 'method_call', 'function': func_name}
            if include_args:
                log_data.update({'args': str(args), 'kwargs': str(kwargs)})
                
            log_structured(logger, level, f"Calling {func_name}", **log_data)
            
            try:
                result = func(*args, **kwargs)
                
                # Log method exit
                exit_data = {'operation': 'method_complete', 'function': func_name}
                if include_result:
                    exit_data['result'] = str(result)[:200]  # Truncate long results
                    
                log_structured(logger, level, f"Completed {func_name}", **exit_data)
                return result
                
            except Exception as e:
                log_structured(
                    logger, logging.ERROR,
                    f"Failed {func_name}: {e}",
                    operation='method_error',
                    function=func_name,
                    error_type=type(e).__name__
                )
                raise
                
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            func_name = f"{func.__qualname__}"
            
            # Log method entry
            log_data = {'operation': 'async_method_call', 'function': func_name}
            if include_args:
                log_data.update({'args': str(args), 'kwargs': str(kwargs)})
                
            log_structured(logger, level, f"Calling async {func_name}", **log_data)
            
            try:
                result = await func(*args, **kwargs)
                
                # Log method exit
                exit_data = {'operation': 'async_method_complete', 'function': func_name}
                if include_result:
                    exit_data['result'] = str(result)[:200]  # Truncate long results
                    
                log_structured(logger, level, f"Completed async {func_name}", **exit_data)
                return result
                
            except Exception as e:
                log_structured(
                    logger, logging.ERROR,
                    f"Failed async {func_name}: {e}",
                    operation='async_method_error',
                    function=func_name,
                    error_type=type(e).__name__
                )
                raise
                
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


def log_database_operations(logger: Optional[logging.Logger] = None,
                           level: int = logging.DEBUG,
                           log_queries: bool = True):
    """Decorator to log database operations"""
    
    def decorator(func: Callable) -> Callable:
        nonlocal logger
        if logger is None:
            logger = logging.getLogger(func.__module__)
            
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            func_name = f"{func.__qualname__}"
            
            try:
                result = func(*args, **kwargs)
                duration = time.perf_counter() - start_time
                
                log_data = {
                    'operation': 'database_operation',
                    'function': func_name,
                    'duration': duration
                }
                
                if log_queries and len(args) > 1:
                    # Assume first argument after self is the query
                    query = str(args[1])[:200] if len(query) > 200 else str(args[1])
                    log_data['query'] = query
                    
                log_structured(
                    logger, level,
                    f"Database operation {func_name} completed in {duration:.3f}s",
                    **log_data
                )
                
                return result
                
            except Exception as e:
                duration = time.perf_counter() - start_time
                log_structured(
                    logger, logging.ERROR,
                    f"Database operation {func_name} failed after {duration:.3f}s: {e}",
                    operation='database_error',
                    function=func_name,
                    duration=duration,
                    error_type=type(e).__name__
                )
                raise
                
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            func_name = f"{func.__qualname__}"
            
            try:
                result = await func(*args, **kwargs)
                duration = time.perf_counter() - start_time
                
                log_data = {
                    'operation': 'async_database_operation',
                    'function': func_name,
                    'duration': duration
                }
                
                if log_queries and len(args) > 1:
                    # Assume first argument after self is the query
                    query = str(args[1])
                    query = query[:200] if len(query) > 200 else query
                    log_data['query'] = query
                    
                log_structured(
                    logger, level,
                    f"Async database operation {func_name} completed in {duration:.3f}s",
                    **log_data
                )
                
                return result
                
            except Exception as e:
                duration = time.perf_counter() - start_time
                log_structured(
                    logger, logging.ERROR,
                    f"Async database operation {func_name} failed after {duration:.3f}s: {e}",
                    operation='async_database_error',
                    function=func_name,
                    duration=duration,
                    error_type=type(e).__name__
                )
                raise
                
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


def log_api_calls(logger: Optional[logging.Logger] = None,
                 level: int = logging.INFO,
                 include_request_data: bool = False):
    """Decorator to log API endpoint calls"""
    
    def decorator(func: Callable) -> Callable:
        nonlocal logger
        if logger is None:
            logger = logging.getLogger(func.__module__)
            
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.perf_counter()
            func_name = f"{func.__qualname__}"
            
            # Extract request information if available
            request = None
            for arg in args:
                if hasattr(arg, 'method') and hasattr(arg, 'url'):
                    request = arg
                    break
                    
            log_data = {
                'operation': 'api_call',
                'endpoint': func_name
            }
            
            if request:
                log_data.update({
                    'method': request.method,
                    'url': str(request.url),
                    'client_ip': request.client.host if request.client else 'unknown'
                })
                
                if include_request_data:
                    log_data['headers'] = dict(request.headers)
                    
            log_structured(logger, level, f"API call to {func_name}", **log_data)
            
            try:
                result = await func(*args, **kwargs)
                duration = time.perf_counter() - start_time
                
                log_structured(
                    logger, level,
                    f"API call to {func_name} completed in {duration:.3f}s",
                    operation='api_complete',
                    endpoint=func_name,
                    duration=duration
                )
                
                return result
                
            except Exception as e:
                duration = time.perf_counter() - start_time
                log_structured(
                    logger, logging.ERROR,
                    f"API call to {func_name} failed after {duration:.3f}s: {e}",
                    operation='api_error',
                    endpoint=func_name,
                    duration=duration,
                    error_type=type(e).__name__
                )
                raise
                
        return wrapper
    return decorator


def log_security_events(logger: Optional[logging.Logger] = None,
                       level: int = logging.WARNING):
    """Decorator to log security-related events"""
    
    def decorator(func: Callable) -> Callable:
        nonlocal logger
        if logger is None:
            logger = logging.getLogger(func.__module__)
            
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            func_name = f"{func.__qualname__}"
            
            try:
                result = func(*args, **kwargs)
                
                log_structured(
                    logger, level,
                    f"Security operation {func_name} completed",
                    operation='security_event',
                    function=func_name,
                    security_event='operation_success'
                )
                
                return result
                
            except Exception as e:
                log_structured(
                    logger, logging.ERROR,
                    f"Security operation {func_name} failed: {e}",
                    operation='security_error',
                    function=func_name,
                    security_event='operation_failure',
                    error_type=type(e).__name__
                )
                raise
                
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            func_name = f"{func.__qualname__}"
            
            try:
                result = await func(*args, **kwargs)
                
                log_structured(
                    logger, level,
                    f"Async security operation {func_name} completed",
                    operation='async_security_event',
                    function=func_name,
                    security_event='operation_success'
                )
                
                return result
                
            except Exception as e:
                log_structured(
                    logger, logging.ERROR,
                    f"Async security operation {func_name} failed: {e}",
                    operation='async_security_error',
                    function=func_name,
                    security_event='operation_failure',
                    error_type=type(e).__name__
                )
                raise
                
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator
