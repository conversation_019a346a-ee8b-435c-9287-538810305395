"""
Authentication middleware
Handles authentication and authorization for routes
"""

from typing import Any, Optional

from ..middleware.base import BaseMiddleware
from ..interfaces import RouteInfo
from .types import AuthType
from ...logging import get_logger

logger = get_logger(__name__)


class AuthMiddleware(BaseMiddleware):
    """Middleware for handling authentication"""
    
    def __init__(self, priority: int = 20):
        super().__init__(priority)
    
    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Process authentication for request"""
        try:
            auth_type = route_info.auth
            
            # Skip authentication for routes that don't require it
            if auth_type == AuthType.NONE:
                return request
            
            # Handle public routes
            if auth_type == AuthType.PUBLIC:
                # Public routes may have optional authentication
                await self._handle_optional_auth(request, route_info)
                return request
            
            # Handle user authentication
            if auth_type == AuthType.USER:
                if not await self._authenticate_user(request, route_info):
                    raise AuthenticationError("User authentication required")
                return request
            
            # Handle admin authentication
            if auth_type == AuthType.ADMIN:
                if not await self._authenticate_admin(request, route_info):
                    raise AuthenticationError("Admin authentication required")
                return request
            
            return request
            
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Error in auth middleware for {route_info.path}: {e}")
            raise AuthenticationError(f"Authentication error: {e}")
    
    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Process response (no auth processing needed)"""
        return response
    
    async def _handle_optional_auth(self, request: Any, route_info: RouteInfo) -> None:
        """Handle optional authentication for public routes"""
        try:
            # Try to authenticate but don't fail if it doesn't work
            await self._authenticate_user(request, route_info)
        except:
            # Authentication failed but that's OK for public routes
            pass
    
    async def _authenticate_user(self, request: Any, route_info: RouteInfo) -> bool:
        """Authenticate user from request"""
        try:
            # Get authentication token/session
            auth_token = self._extract_auth_token(request)
            if not auth_token:
                return False
            
            # Validate token and get user
            user = await self._validate_auth_token(auth_token)
            if not user:
                return False
            
            # Store user in request context
            self._set_user_context(request, user)
            return True
            
        except Exception as e:
            logger.debug(f"User authentication failed: {e}")
            return False
    
    async def _authenticate_admin(self, request: Any, route_info: RouteInfo) -> bool:
        """Authenticate admin user from request"""
        try:
            # First authenticate as user
            if not await self._authenticate_user(request, route_info):
                return False
            
            # Check if user has admin privileges
            user = self._get_user_context(request)
            if not user or not self._is_admin_user(user):
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"Admin authentication failed: {e}")
            return False
    
    def _extract_auth_token(self, request: Any) -> Optional[str]:
        """Extract authentication token from request"""
        try:
            # Check Authorization header
            if hasattr(request, 'headers'):
                auth_header = request.headers.get('Authorization')
                if auth_header and auth_header.startswith('Bearer '):
                    return auth_header[7:]  # Remove 'Bearer ' prefix
            
            # Check session cookie
            if hasattr(request, 'cookies'):
                session_id = request.cookies.get('session_id')
                if session_id:
                    return session_id
            
            # Check query parameter
            if hasattr(request, 'query_params'):
                token = request.query_params.get('token')
                if token:
                    return token
            
            return None
            
        except Exception as e:
            logger.debug(f"Error extracting auth token: {e}")
            return None
    
    async def _validate_auth_token(self, token: str) -> Optional[dict]:
        """Validate authentication token and return user info"""
        try:
            # This would typically validate against a database or external service
            # For now, return a mock user for demonstration
            if token == 'valid_token':
                return {
                    'id': 1,
                    'username': 'testuser',
                    'email': '<EMAIL>',
                    'is_admin': False
                }
            elif token == 'admin_token':
                return {
                    'id': 2,
                    'username': 'admin',
                    'email': '<EMAIL>',
                    'is_admin': True
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error validating auth token: {e}")
            return None
    
    def _set_user_context(self, request: Any, user: dict) -> None:
        """Set user context in request"""
        try:
            # Store user in request state
            if hasattr(request, 'state'):
                request.state.user = user
            else:
                # Fallback: store as attribute
                setattr(request, '_auth_user', user)
        except Exception as e:
            logger.debug(f"Error setting user context: {e}")
    
    def _get_user_context(self, request: Any) -> Optional[dict]:
        """Get user context from request"""
        try:
            if hasattr(request, 'state') and hasattr(request.state, 'user'):
                return request.state.user
            elif hasattr(request, '_auth_user'):
                return getattr(request, '_auth_user')
            return None
        except Exception as e:
            logger.debug(f"Error getting user context: {e}")
            return None
    
    def _is_admin_user(self, user: dict) -> bool:
        """Check if user has admin privileges"""
        return user.get('is_admin', False)


class AuthenticationError(Exception):
    """Raised when authentication fails"""
    pass


class AuthorizationError(Exception):
    """Raised when authorization fails"""
    pass
