"""
Route metadata definitions and utilities
"""

from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union


class RouteType(str, Enum):
    """Route type enumeration"""
    HTTP = "http"
    JSON = "json"
    # TODO: Add website-related route types when needed
    # WEBSITE = "website"


class HttpMethod(str, Enum):
    """HTTP method enumeration"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class AuthType(str, Enum):
    """Authentication type enumeration"""
    NONE = "none"        # No authentication required
    PUBLIC = "public"    # Public access (basic session)
    USER = "user"        # Authenticated user required
    ADMIN = "admin"      # Admin user required


class RouteMetadata:
    """Container for route metadata"""
    
    def __init__(
        self,
        path: str,
        type: RouteType = RouteType.HTTP,
        auth: AuthType = AuthType.USER,
        methods: Optional[List[str]] = None,
        cors: Optional[str] = None,
        csrf: bool = True,
        save_session: bool = True,
        database: Optional[str] = None,
        original_func: Optional[Callable] = None,
        **kwargs
    ):
        self.path = path
        self.type = type
        self.auth = auth
        self.methods = methods or (['POST'] if type == RouteType.JSON else ['GET'])
        self.cors = cors
        self.csrf = csrf
        self.save_session = save_session
        self.database = database
        self.original_func = original_func
        self.extra_params = kwargs
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to dictionary"""
        return {
            'path': self.path,
            'type': self.type,
            'auth': self.auth,
            'methods': self.methods,
            'cors': self.cors,
            'csrf': self.csrf,
            'save_session': self.save_session,
            'database': self.database,
            'original_func': self.original_func,
            **self.extra_params
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RouteMetadata':
        """Create metadata from dictionary"""
        extra_params = {k: v for k, v in data.items() 
                       if k not in {'path', 'type', 'auth', 'methods', 'cors', 
                                   'csrf', 'save_session', 'database', 'original_func'}}
        
        return cls(
            path=data['path'],
            type=data.get('type', RouteType.HTTP),
            auth=data.get('auth', AuthType.USER),
            methods=data.get('methods'),
            cors=data.get('cors'),
            csrf=data.get('csrf', True),
            save_session=data.get('save_session', True),
            database=data.get('database'),
            original_func=data.get('original_func'),
            **extra_params
        )


def normalize_methods(methods: Optional[List[Union[str, HttpMethod]]], route_type: RouteType) -> List[str]:
    """
    Normalize HTTP methods list
    
    Args:
        methods: List of HTTP methods or None
        route_type: Type of route (affects default methods)
        
    Returns:
        List of normalized method strings
    """
    if methods is None:
        return ['POST'] if route_type == RouteType.JSON else ['GET']
    
    normalized = []
    for method in methods:
        if isinstance(method, HttpMethod):
            normalized.append(method.value.upper())
        else:
            normalized.append(str(method).upper())
    
    return normalized


def create_route_metadata(
    path: str,
    type: RouteType = RouteType.HTTP,
    auth: AuthType = AuthType.USER,
    methods: Optional[List[Union[str, HttpMethod]]] = None,
    cors: Optional[str] = None,
    csrf: bool = True,
    save_session: bool = True,
    database: Optional[str] = None,
    original_func: Optional[Callable] = None,
    **kwargs
) -> RouteMetadata:
    """
    Create route metadata with normalized methods
    
    Args:
        path: URL path pattern
        type: Route type (http, json)
        auth: Authentication type
        methods: HTTP methods
        cors: CORS origin pattern
        csrf: Enable CSRF protection
        save_session: Save session after request
        database: Specific database to register route for
        original_func: Original function being decorated
        **kwargs: Additional route parameters
        
    Returns:
        RouteMetadata instance
    """
    normalized_methods = normalize_methods(methods, type)
    
    return RouteMetadata(
        path=path,
        type=type,
        auth=auth,
        methods=normalized_methods,
        cors=cors,
        csrf=csrf,
        save_session=save_session,
        database=database,
        original_func=original_func,
        **kwargs
    )
