"""
Base field classes for ERP models
"""


class FieldValidationError(Exception):
    """Exception raised when field validation fails"""
    pass


class Field:
    """Base field class"""

    def __init__(self, string=None, required=False, readonly=False, default=None,
                 help=None, index=False, unique=False, store=True, compute=None, depends=None,
                 domain=None, context=None, **kwargs):
        self.string = string
        self.required = required
        self.readonly = readonly
        self.default = default
        self.help = help
        self.index = index
        self.unique = unique
        self.store = store
        self.compute = compute
        self.depends = depends or []
        self.domain = domain or []
        self.context = context or {}
        self.kwargs = kwargs

    def __str__(self):
        """String representation of the field"""
        class_name = self.__class__.__name__
        if self.string:
            return f"{class_name}({self.string})"
        return f"{class_name}()"

    def get_default_value(self):
        """Get the default value for this field"""
        if callable(self.default):
            return self.default()
        return self.default

    def get_sql_type(self):
        """Get SQL type for this field"""
        return "TEXT"

    def validate(self, value):
        """Validate field value"""
        if value is None:
            if self.required:
                raise FieldValidationError(f"Field '{self.string or 'unknown'}' is required")
            return None

        # Perform type-specific validation
        return self._validate_value(value)

    def _validate_value(self, value):
        """Override in subclasses for type-specific validation"""
        return value

    def convert_to_cache(self, value):
        """Convert value for caching"""
        return value

    def convert_to_record(self, value):
        """Convert cached value back to record value"""
        return value

    def convert_to_write(self, value):
        """Convert value for database write"""
        return value

    def convert_to_read(self, value):
        """Convert database value for reading"""
        return value


class RelationalField(Field):
    """Base class for relational fields"""

    def __init__(self, comodel_name, **kwargs):
        """
        Initialize relational field

        Args:
            comodel_name: Name of the related model (e.g., 'res.partner')
        """
        super().__init__(**kwargs)
        self.comodel_name = comodel_name

    def get_comodel_class(self):
        """Get the related model class"""
        # TODO: Implement when model registry is available
        return None

    def _validate_comodel_exists(self):
        """Validate that the comodel exists"""
        # TODO: Implement when model registry is available
        pass
