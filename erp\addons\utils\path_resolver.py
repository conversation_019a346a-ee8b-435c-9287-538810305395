"""
Addon Path Resolver Service

This module provides a centralized service for resolving addon paths using the
configuration's addons_paths property. This implements the single source of truth
pattern for addon path resolution across the entire system.
"""

import os
from typing import List, Optional, Dict
from ...config import config
from ...logging import get_logger


class AddonPathResolver:
    """
    Centralized service for resolving addon paths using configuration.
    
    This class provides methods to:
    - Resolve addon paths from configuration
    - Find specific addons across multiple paths
    - Handle path-related operations consistently
    - Provide dynamic imports for addons
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._addon_cache = {}  # Cache for resolved addon paths
    
    @property
    def addons_paths(self) -> List[str]:
        """Get all addon paths from configuration"""
        return config.addons_paths
    
    @property
    def primary_addons_path(self) -> str:
        """Get the primary (first) addon path from configuration"""
        return config.addons_path
    
    def find_addon_path(self, addon_name: str) -> Optional[str]:
        """
        Find the path to a specific addon by searching all configured addon paths.
        
        Args:
            addon_name: Name of the addon to find
            
        Returns:
            Full path to the addon directory, or None if not found
        """
        # Check cache first
        if addon_name in self._addon_cache:
            cached_path = self._addon_cache[addon_name]
            if os.path.exists(cached_path):
                return cached_path
            else:
                # Remove stale cache entry
                del self._addon_cache[addon_name]
        
        # Search in all configured addon paths
        for addons_path in self.addons_paths:
            addon_path = os.path.join(addons_path, addon_name)
            if self._is_valid_addon_directory(addon_path):
                # Cache the result
                self._addon_cache[addon_name] = addon_path
                self.logger.debug(f"Found addon '{addon_name}' at: {addon_path}")
                return addon_path
        
        self.logger.warning(f"Addon '{addon_name}' not found in any configured path")
        return None
    
    def get_addon_manifest_path(self, addon_name: str) -> Optional[str]:
        """
        Get the path to an addon's manifest file.
        
        Args:
            addon_name: Name of the addon
            
        Returns:
            Full path to the __manifest__.py file, or None if not found
        """
        addon_path = self.find_addon_path(addon_name)
        if addon_path:
            manifest_path = os.path.join(addon_path, '__manifest__.py')
            if os.path.exists(manifest_path):
                return manifest_path
        return None
    
    def get_addon_data_files_path(self, addon_name: str, data_file: str) -> Optional[str]:
        """
        Get the full path to a data file within an addon.
        
        Args:
            addon_name: Name of the addon
            data_file: Relative path to the data file within the addon
            
        Returns:
            Full path to the data file, or None if not found
        """
        addon_path = self.find_addon_path(addon_name)
        if addon_path:
            data_file_path = os.path.join(addon_path, data_file)
            if os.path.exists(data_file_path):
                return data_file_path
        return None
    
    def list_all_addons(self) -> Dict[str, str]:
        """
        List all available addons across all configured paths.
        
        Returns:
            Dictionary mapping addon names to their full paths
        """
        all_addons = {}
        
        for addons_path in self.addons_paths:
            if not os.path.exists(addons_path):
                self.logger.warning(f"Addon path does not exist: {addons_path}")
                continue
            
            try:
                for item in os.listdir(addons_path):
                    addon_path = os.path.join(addons_path, item)
                    if self._is_valid_addon_directory(addon_path):
                        if item in all_addons:
                            self.logger.warning(
                                f"Addon '{item}' found in multiple paths: "
                                f"{all_addons[item]} and {addon_path}. "
                                f"Using the first one found."
                            )
                        else:
                            all_addons[item] = addon_path
            except OSError as e:
                self.logger.error(f"Error listing addons in {addons_path}: {e}")
        
        return all_addons
    
    def get_addon_import_path(self, addon_name: str) -> Optional[str]:
        """
        Get the import path for an addon module.
        
        Args:
            addon_name: Name of the addon
            
        Returns:
            Import path string (e.g., 'addons.base'), or None if addon not found
        """
        addon_path = self.find_addon_path(addon_name)
        if addon_path:
            # Determine which addons_path this addon belongs to
            for addons_path in self.addons_paths:
                if addon_path.startswith(addons_path):
                    # For standard addons path, use 'addons.addon_name'
                    if os.path.basename(addons_path) == 'addons':
                        return f'addons.{addon_name}'
                    else:
                        # For custom paths, use the path name
                        path_name = os.path.basename(addons_path)
                        return f'{path_name}.{addon_name}'
        return None
    
    def resolve_relative_path(self, addon_name: str, relative_path: str) -> Optional[str]:
        """
        Resolve a relative path within an addon to an absolute path.
        
        Args:
            addon_name: Name of the addon
            relative_path: Relative path within the addon
            
        Returns:
            Absolute path, or None if addon not found
        """
        addon_path = self.find_addon_path(addon_name)
        if addon_path:
            return os.path.join(addon_path, relative_path)
        return None
    
    def _is_valid_addon_directory(self, path: str) -> bool:
        """
        Check if a directory is a valid addon directory.
        
        Args:
            path: Path to check
            
        Returns:
            True if it's a valid addon directory, False otherwise
        """
        if not os.path.isdir(path):
            return False
        
        # Check for required files
        manifest_file = os.path.join(path, '__manifest__.py')
        init_file = os.path.join(path, '__init__.py')
        
        return os.path.exists(manifest_file) and os.path.exists(init_file)
    
    def clear_cache(self):
        """Clear the addon path cache"""
        self._addon_cache.clear()
        self.logger.debug("Addon path cache cleared")
    
    def validate_configuration(self) -> List[str]:
        """
        Validate the addon paths configuration.
        
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        if not self.addons_paths:
            errors.append("No addon paths configured")
            return errors
        
        for addons_path in self.addons_paths:
            if not os.path.exists(addons_path):
                errors.append(f"Addon path does not exist: {addons_path}")
            elif not os.path.isdir(addons_path):
                errors.append(f"Addon path is not a directory: {addons_path}")
        
        return errors


# Global instance for use throughout the system
addon_path_resolver = AddonPathResolver()


def get_addon_path_resolver() -> AddonPathResolver:
    """Get the global addon path resolver instance"""
    return addon_path_resolver


# Convenience functions for common operations
def find_addon_path(addon_name: str) -> Optional[str]:
    """Find the path to a specific addon"""
    return addon_path_resolver.find_addon_path(addon_name)


def get_addon_manifest_path(addon_name: str) -> Optional[str]:
    """Get the path to an addon's manifest file"""
    return addon_path_resolver.get_addon_manifest_path(addon_name)


def get_addon_data_file_path(addon_name: str, data_file: str) -> Optional[str]:
    """Get the full path to a data file within an addon"""
    return addon_path_resolver.get_addon_data_files_path(addon_name, data_file)


def list_all_addons() -> Dict[str, str]:
    """List all available addons"""
    return addon_path_resolver.list_all_addons()
