"""
Addon hook implementation

This module defines the AddonHook class that represents individual hooks.
"""
import asyncio
import time
from typing import Callable, Any

from .hook_types import HookType
from .hook_context import Hook<PERSON>ontext
from ...logging import get_logger

logger = get_logger(__name__)


class AddonHook:
    """Represents a single addon hook"""
    
    # Type annotations for instance attributes
    func: Callable[[HookContext], Any]
    hook_type: HookType
    addon_name: str
    priority: int
    is_async: bool
    
    def __init__(
        self,
        func: Callable[[HookContext], Any],
        hook_type: HookType,
        addon_name: str,
        priority: int = 50
    ) -> None:
        self.func = func
        self.hook_type = hook_type
        self.addon_name = addon_name
        self.priority = priority
        self.is_async = asyncio.iscoroutinefunction(func)
    
    async def execute(self, context: HookContext) -> Any:
        """Execute the hook function"""
        start_time = time.perf_counter()

        try:
            if self.is_async:
                result = await self.func(context)
            else:
                result = self.func(context)

            duration = time.perf_counter() - start_time
            # Only log if it takes significant time or fails
            if duration > 0.1:  # Log if takes more than 100ms
                logger.debug(f"Executed {self.hook_type.value} hook for {self.addon_name} in {duration:.3f}s")
            return result

        except Exception as e:
            duration = time.perf_counter() - start_time
            logger.error(f"Error executing {self.hook_type.value} hook for {self.addon_name} after {duration:.3f}s: {e}")
            raise
    
    def __repr__(self) -> str:
        return f"AddonHook(addon={self.addon_name}, type={self.hook_type.value}, priority={self.priority})"
