"""
Test suite for modular addon installers

This module tests:
- AddonInstaller functionality
- BaseModuleInstaller functionality
- InstallationContext and utilities
- Installation process flow
- Error handling in installers
"""
import pytest
from unittest.mock import MagicMock, AsyncMock, patch
from pathlib import Path

from erp.addons.installers import (
    AddonInstaller,
    BaseModuleInstaller,
    InstallationContext,
    InstallationUtilities,
    RegistryExtractor
)


class TestInstallationContext:
    """Test InstallationContext functionality"""
    
    def test_installation_context_creation(self):
        """Test InstallationContext creation"""
        mock_env = MagicMock()
        
        context = InstallationContext(
            env=mock_env,
            addon_name="test_addon",
            addon_path="/path/to/addon",
            operation="install"
        )
        
        assert context.env == mock_env
        assert context.addon_name == "test_addon"
        assert context.addon_path == "/path/to/addon"
        assert context.operation == "install"
    
    def test_installation_context_with_metadata(self):
        """Test InstallationContext with metadata"""
        mock_env = MagicMock()
        metadata = {"version": "1.0.0", "author": "Test Author"}
        
        context = InstallationContext(
            env=mock_env,
            addon_name="test_addon",
            addon_path="/path/to/addon",
            operation="install",
            metadata=metadata
        )
        
        assert context.metadata == metadata


class TestInstallationUtilities:
    """Test InstallationUtilities functionality"""
    
    def test_installation_utilities_creation(self):
        """Test InstallationUtilities creation"""
        mock_env = MagicMock()
        utilities = InstallationUtilities(mock_env)
        assert utilities.env == mock_env
    
    @pytest.mark.asyncio
    async def test_validate_addon_structure(self):
        """Test addon structure validation"""
        mock_env = MagicMock()
        utilities = InstallationUtilities(mock_env)
        
        # Mock the validation method
        with patch.object(utilities, 'validate_addon_structure', return_value=True) as mock_validate:
            result = await utilities.validate_addon_structure("/path/to/addon")
            assert result is True
            mock_validate.assert_called_once_with("/path/to/addon")
    
    @pytest.mark.asyncio
    async def test_check_dependencies(self):
        """Test dependency checking"""
        mock_env = MagicMock()
        utilities = InstallationUtilities(mock_env)
        
        dependencies = ["base", "web"]
        
        with patch.object(utilities, 'check_dependencies', return_value={"missing": [], "satisfied": dependencies}) as mock_check:
            result = await utilities.check_dependencies(dependencies)
            assert result["satisfied"] == dependencies
            assert result["missing"] == []
            mock_check.assert_called_once_with(dependencies)


class TestRegistryExtractor:
    """Test RegistryExtractor functionality"""
    
    def test_registry_extractor_creation(self):
        """Test RegistryExtractor creation"""
        mock_env = MagicMock()
        extractor = RegistryExtractor(mock_env)
        assert extractor.env == mock_env
    
    @pytest.mark.asyncio
    async def test_extract_models(self):
        """Test model extraction"""
        mock_env = MagicMock()
        extractor = RegistryExtractor(mock_env)
        
        with patch.object(extractor, 'extract_models', return_value=["model1", "model2"]) as mock_extract:
            result = await extractor.extract_models("test_addon")
            assert result == ["model1", "model2"]
            mock_extract.assert_called_once_with("test_addon")
    
    @pytest.mark.asyncio
    async def test_extract_routes(self):
        """Test route extraction"""
        mock_env = MagicMock()
        extractor = RegistryExtractor(mock_env)
        
        with patch.object(extractor, 'extract_routes', return_value=["route1", "route2"]) as mock_extract:
            result = await extractor.extract_routes("test_addon")
            assert result == ["route1", "route2"]
            mock_extract.assert_called_once_with("test_addon")


class TestAddonInstaller:
    """Test AddonInstaller functionality"""
    
    def test_addon_installer_creation(self):
        """Test AddonInstaller creation"""
        mock_env = MagicMock()
        installer = AddonInstaller(mock_env)
        assert installer.env == mock_env
    
    @pytest.mark.asyncio
    async def test_install_addon_success(self):
        """Test successful addon installation"""
        mock_env = MagicMock()
        installer = AddonInstaller(mock_env)
        
        # Mock the install method
        with patch.object(installer, 'install', return_value={"success": True, "message": "Installed successfully"}) as mock_install:
            result = await installer.install("test_addon", "/path/to/addon")
            assert result["success"] is True
            assert "success" in result["message"].lower()
            mock_install.assert_called_once_with("test_addon", "/path/to/addon")
    
    @pytest.mark.asyncio
    async def test_install_addon_failure(self):
        """Test failed addon installation"""
        mock_env = MagicMock()
        installer = AddonInstaller(mock_env)
        
        with patch.object(installer, 'install', return_value={"success": False, "error": "Installation failed"}) as mock_install:
            result = await installer.install("test_addon", "/path/to/addon")
            assert result["success"] is False
            assert "error" in result
            mock_install.assert_called_once_with("test_addon", "/path/to/addon")
    
    @pytest.mark.asyncio
    async def test_uninstall_addon(self):
        """Test addon uninstallation"""
        mock_env = MagicMock()
        installer = AddonInstaller(mock_env)
        
        with patch.object(installer, 'uninstall', return_value={"success": True, "message": "Uninstalled successfully"}) as mock_uninstall:
            result = await installer.uninstall("test_addon")
            assert result["success"] is True
            mock_uninstall.assert_called_once_with("test_addon")
    
    @pytest.mark.asyncio
    async def test_upgrade_addon(self):
        """Test addon upgrade"""
        mock_env = MagicMock()
        installer = AddonInstaller(mock_env)
        
        with patch.object(installer, 'upgrade', return_value={"success": True, "message": "Upgraded successfully"}) as mock_upgrade:
            result = await installer.upgrade("test_addon", "/new/path/to/addon")
            assert result["success"] is True
            mock_upgrade.assert_called_once_with("test_addon", "/new/path/to/addon")


class TestBaseModuleInstaller:
    """Test BaseModuleInstaller functionality"""
    
    def test_base_module_installer_creation(self):
        """Test BaseModuleInstaller creation"""
        mock_env = MagicMock()
        installer = BaseModuleInstaller(mock_env)
        assert installer.env == mock_env
    
    @pytest.mark.asyncio
    async def test_install_base_module(self):
        """Test base module installation"""
        mock_env = MagicMock()
        installer = BaseModuleInstaller(mock_env)
        
        with patch.object(installer, 'install_base_module', return_value={"success": True, "message": "Base module installed"}) as mock_install:
            result = await installer.install_base_module("/path/to/base")
            assert result["success"] is True
            mock_install.assert_called_once_with("/path/to/base")
    
    @pytest.mark.asyncio
    async def test_bootstrap_system(self):
        """Test system bootstrap"""
        mock_env = MagicMock()
        installer = BaseModuleInstaller(mock_env)
        
        with patch.object(installer, 'bootstrap_system', return_value={"success": True, "message": "System bootstrapped"}) as mock_bootstrap:
            result = await installer.bootstrap_system()
            assert result["success"] is True
            mock_bootstrap.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_clean_bootstrap(self):
        """Test clean bootstrap process"""
        mock_env = MagicMock()
        installer = BaseModuleInstaller(mock_env)
        
        with patch.object(installer, 'clean_bootstrap', return_value={"success": True, "message": "Clean bootstrap completed"}) as mock_clean:
            result = await installer.clean_bootstrap()
            assert result["success"] is True
            mock_clean.assert_called_once()


class TestInstallerIntegration:
    """Test installer integration and workflow"""
    
    @pytest.mark.asyncio
    async def test_installer_workflow(self):
        """Test complete installer workflow"""
        mock_env = MagicMock()
        
        # Create context
        context = InstallationContext(
            env=mock_env,
            addon_name="test_addon",
            addon_path="/path/to/addon",
            operation="install"
        )
        
        # Create utilities
        utilities = InstallationUtilities(mock_env)
        
        # Create installer
        installer = AddonInstaller(mock_env)
        
        # Mock the workflow steps
        with patch.object(utilities, 'validate_addon_structure', return_value=True), \
             patch.object(utilities, 'check_dependencies', return_value={"missing": [], "satisfied": []}), \
             patch.object(installer, 'install', return_value={"success": True}):
            
            # Simulate workflow
            structure_valid = await utilities.validate_addon_structure(context.addon_path)
            assert structure_valid is True
            
            deps_check = await utilities.check_dependencies([])
            assert deps_check["missing"] == []
            
            install_result = await installer.install(context.addon_name, context.addon_path)
            assert install_result["success"] is True
    
    def test_all_installer_components_importable(self):
        """Test that all installer components can be imported"""
        from erp.addons.installers import __all__
        
        expected_components = [
            'AddonInstaller',
            'BaseModuleInstaller',
            'InstallationContext',
            'InstallationUtilities',
            'RegistryExtractor'
        ]
        
        for component in expected_components:
            assert component in __all__
