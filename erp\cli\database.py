"""
Database management commands
"""
import argparse
import asyncio

from .base import BaseCommand, CommandGroup
from ..config import config
from ..database.registry import DatabaseRegistry
from ..database.registry.lifecycle import DatabaseLifecycle
from ..database.registry.initialization import DatabaseInitializer


class InitCommand(BaseCommand):
    """Initialize database command with positional database name"""
    
    def add_arguments(self, parser: argparse.ArgumentParser):
        parser.add_argument('db_name', help='Database name to initialize')
        parser.add_argument('--force', action='store_true', help='Force initialization even if database exists')
        parser.add_argument('--demo', action='store_true', help='Install demo data')
        parser.add_argument('--no-create', action='store_true', help='Do not create database if it does not exist')
        parser.add_argument('--exit', action='store_true', help='Exit after initialization instead of starting server')
        parser.add_argument('--no-http', action='store_true', help='Skip HTTP server startup (only applicable if not using --exit)')
    
    def handle(self, args: argparse.Namespace) -> int:
        """Handle database initialization command"""
        import asyncio
        
        async def _init_database():
            try:
                db_name = args.db_name
                
                self.print_info(f"🚀 Initializing database: {db_name}")
                
                # Check if database exists first
                existing_dbs = await DatabaseRegistry.list_databases()
                db_exists = db_name in existing_dbs
                
                if db_exists and not args.force:
                    self.print_warning(f"Database '{db_name}' already exists. Use --force to reinitialize.")
                    return 1
                
                # Create database if it doesn't exist (unless --no-create flag is used)
                if not db_exists:
                    if args.no_create:
                        self.print_error(f"Database '{db_name}' does not exist and --no-create flag was specified.")
                        return 1
                    else:
                        self.print_info(f"📊 Creating database: {db_name}")
                        created = await DatabaseLifecycle.create_database(db_name)
                        if not created:
                            self.print_error(f"Failed to create database: {db_name}")
                            return 1
                        self.print_success(f"✅ Database '{db_name}' created successfully")
                
                # Initialize the database
                self.print_info("🔧 Installing base modules and setting up initial data...")
                success = await DatabaseInitializer.initialize_database(db_name)
                
                if success:
                    self.print_success(f"✅ Database '{db_name}' initialized successfully!")
                    self.print_info("📋 Database includes:")
                    self.print_info("  - Base modules and tables")
                    self.print_info("  - Core ERP functionality")
                    if args.demo:
                        self.print_info("  - Demo data (if available)")
                    
                    # Set the initialized database as the current database in config
                    config.set('options', 'db_name', db_name)
                    
                    # If --exit flag is used, exit after initialization
                    if args.exit:
                        self.print_info("🚪 Exiting after initialization (--exit flag specified)")
                        return 0
                    
                    # Otherwise, start the server with the initialized database
                    return self._start_server_after_init(args, db_name)
                else:
                    self.print_error(f"❌ Failed to initialize database: {db_name}")
                    return 1
                    
            except Exception as e:
                self.print_error(f"💥 Failed to initialize database: {e}")
                if args.verbose if hasattr(args, 'verbose') else False:
                    import traceback
                    traceback.print_exc()
                return 1
        
        # Run the async function
        return asyncio.run(_init_database())
    
    def _start_server_after_init(self, args: argparse.Namespace, db_name: str) -> int:
        """Start the ERP server after successful database initialization"""
        try:
            if args.no_http:
                self.print_info("🚫 HTTP server startup skipped (--no-http flag specified)")
                self.print_info(f"📊 Database '{db_name}' is ready and selected as current database")
                self.print_info("💡 You can start the HTTP server later with: erp-bin start")
                
                # Keep the process running without HTTP server
                self.print_info("🔄 Keeping process running with initialized database...")
                self.print_info("   Press Ctrl+C to exit")
                
                try:
                    import signal
                    import time
                    
                    def signal_handler(signum, frame):
                        self.print_info("\n🛑 Received shutdown signal, exiting...")
                        exit(0)
                    
                    signal.signal(signal.SIGINT, signal_handler)
                    signal.signal(signal.SIGTERM, signal_handler)
                    
                    # Keep running until interrupted
                    while True:
                        time.sleep(1)
                        
                except KeyboardInterrupt:
                    self.print_info("\n🛑 Process interrupted by user")
                    return 0
            else:
                # Since server commands are removed, just exit after initialization
                self.print_info(f"🌐 Database '{db_name}' initialized successfully!")
                self.print_info("💡 Server commands have been removed. Database is ready for use.")
                return 0
                
        except Exception as e:
            self.print_error(f"💥 Failed to complete initialization: {e}")
            if getattr(args, 'verbose', False):
                import traceback
                traceback.print_exc()
            return 1


class DatabaseCommandGroup(CommandGroup):
    """Database command group"""
    
    def __init__(self):
        super().__init__()
        self.register_command(InitCommand())
    
    def add_commands(self, subparsers, parent_parser=None):
        """Add database commands to subparsers"""
        # Initialize database (new command with positional db_name)
        init_parser = subparsers.add_parser('init', help='Initialize database with name', parents=[parent_parser] if parent_parser else [])
        self.commands['init'].add_arguments(init_parser)