"""
Different model types: Abstract, Transient, and Standard models
"""
from datetime import datetime, timedelta
from ..logging import get_logger
from .base_model import BaseModel
from .crud_operations import CRUDOperationsMixin


class AbstractModel(BaseModel):
    """
    Abstract model for shared functionality without database table.
    
    AbstractModel is used for models that provide shared functionality
    but don't need their own database table. Other models inherit from
    abstract models to get common fields and methods.
    
    Key characteristics:
    - No database table created
    - Cannot be instantiated directly
    - Used for inheritance only
    - Provides shared fields and methods
    """
    
    _abstract = True  # Mark as abstract
    _auto_create_table = False  # Don't create database table


class TransientModel(BaseModel, CRUDOperationsMixin):
    """
    Transient model for temporary data (wizards, reports, etc.).
    
    TransientModel is used for temporary data that doesn't need to be
    permanently stored. Records are automatically cleaned up after
    a certain time period.
    
    Key characteristics:
    - Temporary data storage
    - Automatic cleanup of old records
    - Used for wizards, reports, temporary calculations
    - Records expire after _transient_max_hours
    """
    
    _transient = True  # Mark as transient
    _auto_create_table = True  # Create table for temporary storage
    _transient_max_hours = 1  # Default cleanup time (1 hour)
    
    @classmethod
    async def _transient_vacuum(cls):
        """Clean up old transient records"""
        if not cls._transient:
            return
            
        logger = get_logger(f"{__name__}.{cls._name or cls.__name__}")
        logger.debug(f"Running transient vacuum for {cls._name}")

        # Calculate cutoff time
        cutoff_time = datetime.now() - timedelta(hours=cls._transient_max_hours)

        try:
            # Delete records older than cutoff time
            domain = [('createAt', '<', cutoff_time)]
            old_records = await cls.search(domain)

            if old_records:
                await old_records.unlink()
                logger.info(f"Cleaned up {len(old_records)} old {cls._name} records")
        except Exception as e:
            logger.error(f"Error cleaning up transient records for {cls._name}: {e}")


class Model(BaseModel, CRUDOperationsMixin):
    """
    Standard persistent model for business data.

    Model is the standard class for persistent business models that are
    fully backed by database tables. This is what most business models
    should inherit from.

    Key characteristics:
    - Fully backed by database table
    - Persistent data storage
    - Full CRUD operations supported
    - Indexed for performance
    - Used for most business models
    """

    _auto_create_table = True  # Standard models create tables
    _transient = False  # Not transient

    # Standard models include all common fields
    # (inherited from BaseModel: id, name, createAt, updateAt)
