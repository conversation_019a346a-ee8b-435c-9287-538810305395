"""
Server lifecycle management
"""
import time
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse

from ..database.registry import DatabaseRegistry
from ..logging import get_logger


class LifecycleManager:
    """Manages server startup and shutdown lifecycle"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def create_lifespan(self):
        """Create lifespan context manager for FastAPI"""
        
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            # Startup
            startup_start = time.perf_counter()
            self.logger.info("🚀 Starting ERP server...")

            try:
                # Setup template directories and load all templates
                self.logger.debug("Setting up template directories and loading templates")
                await self._setup_template_directories()

                # Load all templates into system_templates during startup
                self.logger.debug("Loading all templates into system_templates")
                template_count = await self._load_templates()
                self.logger.info(f"✅ Loaded {template_count} template files into system_templates")

                # Setup HTTP routes
                self.logger.debug("Setting up HTTP routes")
                await self._setup_http_routes(app)
                self.logger.debug("HTTP routes registered")

                startup_duration = time.perf_counter() - startup_start
                self.logger.info(f"✅ ERP server startup completed in {startup_duration:.3f}s")
                print()

            except Exception as e:
                startup_duration = time.perf_counter() - startup_start
                self.logger.error(f"❌ ERP server startup failed after {startup_duration:.3f}s: {e}")
                raise

            yield

            # Shutdown
            await self._handle_shutdown()
        
        return lifespan
    
    async def _setup_template_directories(self):
        """Setup template directories from default locations"""
        from ..templates.manager import get_template_manager
        template_manager = get_template_manager()
        
        # Add default template directory
        import os
        if os.path.exists("templates"):
            template_manager.add_template_directory("templates")

        print(f"Template directories: {template_manager.template_dirs}")

    async def _load_templates(self):
        """Load all templates into system_templates"""
        from ..templates.manager import get_template_manager
        template_manager = get_template_manager()
        return await template_manager.load_all_templates_async()

    async def _setup_http_routes(self, app: FastAPI):
        """Setup HTTP routes"""
        from ..http import setup_http_routes

        # Register ONLY global/system routes (from @route decorators)
        # NO database routes, NO addon routes during server startup
        setup_http_routes(app)
        self.logger.debug("System HTTP routes registered")

        # Database-specific routes will be registered lazily when databases are accessed
        self.logger.debug("Database routes will be registered lazily when needed")

    async def _handle_shutdown(self):
        """Handle server shutdown"""
        shutdown_start = time.perf_counter()
        self.logger.info("🛑 Shutting down ERP server...")

        # Capture memory usage before shutdown
        from ..logging.monitoring import get_memory_usage, log_memory_freed
        from ..cluster import cluster_manager
        
        before_memory = get_memory_usage()
        self.logger.info(
            f"📊 Memory usage before shutdown: "
            f"Process RSS: {before_memory.get('process_rss_mb', 0):.2f} MB, "
            f"System: {before_memory.get('system_used_mb', 0):.2f} MB "
            f"({before_memory.get('system_percent', 0):.1f}%)"
        )
        
        # Log cluster-specific shutdown information
        cluster_manager.log_cluster_shutdown_info()

        try:
            await DatabaseRegistry.close_all()
            
            # Capture memory usage after shutdown
            after_memory = get_memory_usage()
            
            # Log memory freed
            log_memory_freed(self.logger, before_memory, after_memory)
            
            shutdown_duration = time.perf_counter() - shutdown_start
            self.logger.info(f"✅ ERP server shutdown completed in {shutdown_duration:.3f}s")
        except Exception as e:
            # Still try to log memory even if shutdown failed
            try:
                after_memory = get_memory_usage()
                log_memory_freed(self.logger, before_memory, after_memory)
            except Exception:
                pass  # Don't let memory logging errors mask the original error
                
            shutdown_duration = time.perf_counter() - shutdown_start
            self.logger.error(f"❌ ERP server shutdown failed after {shutdown_duration:.3f}s: {e}")
    
    def setup_exception_handlers(self, app: FastAPI):
        """Setup custom exception handlers with route-aware error responses"""
        from ..http.core.error_detection import ErrorResponseFactory
        from fastapi.exceptions import RequestValidationError
        from starlette.exceptions import HTTPException as StarletteHTTPException

        @app.exception_handler(StarletteHTTPException)
        async def custom_starlette_exception_handler(request: Request, exc: StarletteHTTPException):
            """Custom handler for Starlette HTTP exceptions (including 404s)"""
            try:
                # Create a generic exception from HTTPException for consistent handling
                error = Exception(exc.detail if isinstance(exc.detail, str) else str(exc.detail))

                return ErrorResponseFactory.create_error_response(
                    error=error,
                    request=request,
                    status_code=exc.status_code
                )

            except Exception as e:
                # Fallback to basic JSON response if error handling fails
                self.logger.error(f"Error in Starlette exception handler: {e}")
                return JSONResponse(
                    status_code=exc.status_code,
                    content={"error": str(exc.detail)}
                )

        @app.exception_handler(HTTPException)
        async def custom_http_exception_handler(request: Request, exc: HTTPException):
            """Custom handler with route-aware error responses"""
            try:
                # Create a generic exception from HTTPException for consistent handling
                error = Exception(exc.detail if isinstance(exc.detail, str) else str(exc.detail))

                return ErrorResponseFactory.create_error_response(
                    error=error,
                    request=request,
                    status_code=exc.status_code
                )

            except Exception as e:
                # Fallback to basic JSON response if error handling fails
                self.logger.error(f"Error in exception handler: {e}")
                return JSONResponse(
                    status_code=exc.status_code,
                    content={"error": str(exc.detail)}
                )

        @app.exception_handler(Exception)
        async def generic_exception_handler(request: Request, exc: Exception):
            """Generic exception handler for unhandled exceptions"""
            try:
                self.logger.error(f"Unhandled exception: {exc}")

                return ErrorResponseFactory.create_error_response(
                    error=exc,
                    request=request,
                    status_code=500
                )
            except Exception as e:
                # Ultimate fallback
                self.logger.error(f"Error in generic exception handler: {e}")
                return JSONResponse(
                    status_code=500,
                    content={"error": "Internal server error"}
                )
