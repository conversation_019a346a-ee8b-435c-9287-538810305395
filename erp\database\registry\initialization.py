"""
Database initialization logic
"""
from .database_registry import DatabaseRegistry
from ...logging import get_logger


class DatabaseInitializer:
    """Database initialization with addon system"""
    
    _logger = get_logger(__name__)
    
    @classmethod
    async def initialize_database(cls, db_name: str) -> bool:
        """Initialize database with required tables and data"""
        try:
            from ...addons import AddonManager
            from ...environment import EnvironmentManager

            # Ensure database connection exists
            await DatabaseRegistry.get_database(db_name)

            # Set as current database early for addon installation
            DatabaseRegistry.set_current_database(db_name)

            # Create environment for addon installation
            env = await EnvironmentManager.create_environment(db_name, 1)  # Use admin user

            # Create addon manager and install base addon
            manager = AddonManager()
            await manager.discover_addons()

            # Install base addon within a transaction context
            # This ensures proper transaction management for CLI operations
            async with EnvironmentManager.transaction(env):
                success = await manager.install_addon('base', force=True, env=env)

                if not success:
                    cls._logger.error(f"Base addon installation failed for database {db_name}")
                    # Transaction will be automatically rolled back
                    raise Exception(f"Base addon installation failed for database {db_name}")

            # Transaction is committed at this point, so registry update can see the data
            if not success:
                # Rollback database creation
                from .lifecycle import DatabaseLifecycle
                await DatabaseLifecycle.rollback_database_creation(db_name)
                return False

            cls._logger.info(f"✓ Base addon installed successfully")

            # Now update the registry after transaction commit
            # For base module installation during database initialization, we use immediate refresh
            # since there's no installation session context
            cls._logger.debug("Updating registry after transaction commit...")
            try:
                from ...database.memory.registry_manager import MemoryRegistryManager
                registry_success = await MemoryRegistryManager.update_registry_after_module_action(
                    db_name, 'base', 'install'
                )
                if registry_success:
                    cls._logger.debug(f"✅ Registry successfully updated for database '{db_name}'")
                else:
                    cls._logger.warning(f"⚠️ Registry update failed for database '{db_name}' - continuing anyway")
            except Exception as e:
                cls._logger.warning(f"⚠️ Registry update failed for database '{db_name}': {e} - continuing anyway")
            cls._logger.info(f"Database {db_name} initialized successfully")
            return True

        except Exception as e:
            cls._logger.error(f"Error initializing database {db_name}: {e}")
            # Rollback database creation on any error
            from .lifecycle import DatabaseLifecycle
            await DatabaseLifecycle.rollback_database_creation(db_name)
            return False
