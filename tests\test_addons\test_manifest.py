"""
Test suite for addon manifest parsing and validation

This module tests:
- Manifest file loading and parsing
- Manifest property access and defaults
- Error handling for invalid manifests
- Manifest validation and constraints
"""
import pytest
import tempfile
import os
from pathlib import Path

from erp.addons.manifest import AddonManifest


class TestAddonManifest:
    """Test AddonManifest functionality"""
    
    def test_manifest_initialization(self, tmp_path):
        """Test manifest initialization with valid path"""
        addon_path = tmp_path / "test_addon"
        addon_path.mkdir()
        
        manifest = AddonManifest(str(addon_path))
        
        assert manifest.addon_path == str(addon_path)
        assert manifest.addon_name == "test_addon"
        assert manifest._manifest_data == {}  # No manifest file exists
    
    def test_manifest_loading_valid_file(self, tmp_path):
        """Test loading valid manifest file"""
        addon_path = tmp_path / "test_addon"
        addon_path.mkdir()
        
        manifest_content = """{
    'name': 'Test Addon',
    'version': '1.0.0',
    'description': 'A test addon',
    'author': 'Test Author',
    'depends': ['base'],
    'installable': True
}"""
        
        manifest_file = addon_path / "__manifest__.py"
        manifest_file.write_text(manifest_content)
        
        manifest = AddonManifest(str(addon_path))
        
        assert manifest.name == "Test Addon"
        assert manifest.version == "1.0.0"
        assert manifest.description == "A test addon"
        assert manifest.author == "Test Author"
        assert manifest.depends == ['base']
        assert manifest.installable is True
    
    def test_manifest_properties_defaults(self, tmp_path):
        """Test manifest properties with default values"""
        addon_path = tmp_path / "test_addon"
        addon_path.mkdir()
        
        manifest = AddonManifest(str(addon_path))
        
        assert manifest.name == "test_addon"  # Falls back to directory name
        assert manifest.version == "1.0.0"
        assert manifest.description == ""
        assert manifest.author == ""
        assert manifest.depends == []
        assert manifest.installable is True  # Default value
    
    def test_manifest_loading_invalid_file(self, tmp_path):
        """Test loading invalid manifest file"""
        addon_path = tmp_path / "test_addon"
        addon_path.mkdir()
        
        manifest_file = addon_path / "__manifest__.py"
        manifest_file.write_text("invalid python syntax {")
        
        manifest = AddonManifest(str(addon_path))
        
        # Should fall back to empty manifest data
        assert manifest._manifest_data == {}
        assert manifest.name == "test_addon"
