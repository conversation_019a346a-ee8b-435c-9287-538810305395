"""
Logging configuration management
"""
from typing import Dict, Any, List, Optional


class LoggingConfig:
    """Logging-specific configuration"""
    
    def __init__(self, base_config):
        self._config = base_config
    
    @property
    def config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        return {
            # Basic settings
            'log_level': self._config.get('options', 'log_level', 'info'),
            'log_file': self._config.get('options', 'log_file', 'logs/erp.log'),

            # Console logging
            'log_console_enabled': self._config.getboolean('options', 'log_console_enabled', True),
            'log_console_level': self._config.get('options', 'log_console_level', 'info'),
            'log_colored_console': self._config.getboolean('options', 'log_colored_console', True),

            # File logging
            'log_file_enabled': self._config.getboolean('options', 'log_file_enabled', True),
            'log_file_level': self._config.get('options', 'log_file_level', 'debug'),
            'log_file_rotating': self._config.getboolean('options', 'log_file_rotating', True),
            'log_file_max_bytes': self._config.getint('options', 'log_file_max_bytes', 10485760),
            'log_file_backup_count': self._config.getint('options', 'log_file_backup_count', 5),
            'log_file_json': self._config.getboolean('options', 'log_file_json', False),

            # Timed rotating
            'log_file_timed_rotating': self._config.getboolean('options', 'log_file_timed_rotating', False),
            'log_file_when': self._config.get('options', 'log_file_when', 'midnight'),
            'log_file_interval': self._config.getint('options', 'log_file_interval', 1),

            # JSON logging
            'log_json_enabled': self._config.getboolean('options', 'log_json_enabled', False),
            'log_json_include_extra': self._config.getboolean('options', 'log_json_include_extra', True),

            # Database logging
            'log_database_enabled': self._config.getboolean('options', 'log_database_enabled', False),
            'log_database_level': self._config.get('options', 'log_database_level', 'warning'),
            'log_database_table': self._config.get('options', 'log_database_table', 'system_logs'),
            'log_database_buffer_size': self._config.getint('options', 'log_database_buffer_size', 100),

            # Performance logging
            'log_performance_enabled': self._config.getboolean('options', 'log_performance_enabled', True),
            'log_performance_threshold': self._config.getfloat('options', 'log_performance_threshold', 1.0),

            # Security logging
            'log_security_enabled': self._config.getboolean('options', 'log_security_enabled', True),
            'log_security_level': self._config.get('options', 'log_security_level', 'warning'),

            # Rate limiting
            'log_rate_limit_enabled': self._config.getboolean('options', 'log_rate_limit_enabled', False),
            'log_rate_limit_max_records': self._config.getint('options', 'log_rate_limit_max_records', 100),
            'log_rate_limit_time_window': self._config.getfloat('options', 'log_rate_limit_time_window', 60.0),

            # Duplicate filtering
            'log_duplicate_filter_enabled': self._config.getboolean('options', 'log_duplicate_filter_enabled', False),
            'log_duplicate_max_count': self._config.getint('options', 'log_duplicate_max_count', 5),
            'log_duplicate_time_window': self._config.getfloat('options', 'log_duplicate_time_window', 60.0),

            # Module filtering
            'log_module_filter_enabled': self._config.getboolean('options', 'log_module_filter_enabled', False),
            'log_module_filter_include': self._parse_list('options', 'log_module_filter_include'),
            'log_module_filter_exclude': self._parse_list('options', 'log_module_filter_exclude'),

            # Handlers
            'log_handlers': self._parse_list('options', 'log_handlers', ['console', 'file']),
        }
    
    def _parse_list(self, section: str, option: str, default: Optional[List[str]] = None) -> List[str]:
        """Parse comma-separated list from config"""
        return self._config._parse_list(section, option, default)
    
    # Basic settings
    @property
    def level(self) -> str:
        """Log level"""
        return self._config.get('options', 'log_level', 'info')
    
    @property
    def file(self) -> str:
        """Log file path"""
        return self._config.get('options', 'log_file', 'logs/erp.log')
    
    # Console logging
    @property
    def console_enabled(self) -> bool:
        """Console logging enabled"""
        return self._config.getboolean('options', 'log_console_enabled', True)
    
    @property
    def console_level(self) -> str:
        """Console log level"""
        return self._config.get('options', 'log_console_level', 'info')
    
    @property
    def colored_console(self) -> bool:
        """Colored console output"""
        return self._config.getboolean('options', 'log_colored_console', True)
    
    # File logging
    @property
    def file_enabled(self) -> bool:
        """File logging enabled"""
        return self._config.getboolean('options', 'log_file_enabled', True)
    
    @property
    def file_level(self) -> str:
        """File log level"""
        return self._config.get('options', 'log_file_level', 'debug')
    
    @property
    def file_rotating(self) -> bool:
        """File rotation enabled"""
        return self._config.getboolean('options', 'log_file_rotating', True)
    
    @property
    def file_max_bytes(self) -> int:
        """Maximum file size in bytes"""
        return self._config.getint('options', 'log_file_max_bytes', 10485760)
    
    @property
    def file_backup_count(self) -> int:
        """Number of backup files"""
        return self._config.getint('options', 'log_file_backup_count', 5)
    
    @property
    def handlers(self) -> List[str]:
        """Log handlers"""
        return self._parse_list('options', 'log_handlers', ['console', 'file'])
    
    # Performance logging
    @property
    def performance_enabled(self) -> bool:
        """Performance logging enabled"""
        return self._config.getboolean('options', 'log_performance_enabled', True)
    
    @property
    def performance_threshold(self) -> float:
        """Performance logging threshold"""
        return self._config.getfloat('options', 'log_performance_threshold', 1.0)
    
    # Security logging
    @property
    def security_enabled(self) -> bool:
        """Security logging enabled"""
        return self._config.getboolean('options', 'log_security_enabled', True)
    
    @property
    def security_level(self) -> str:
        """Security log level"""
        return self._config.get('options', 'log_security_level', 'warning')
