"""
XML Data Parser for ERP system

Parses XML data files and extracts record definitions for loading into the database.
Similar to Odoo's XML data parsing functionality.
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import logging

from .exceptions import XMLParsingError
from ..logging import get_logger


class XMLDataParser:
    """Parser for XML data files"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
    def parse_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Parse an XML data file and return list of record definitions
        
        Args:
            file_path: Path to the XML file
            
        Returns:
            List of record dictionaries
        """
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            return self._parse_data_element(root)
            
        except ET.ParseError as e:
            raise XMLParsingError(f"Failed to parse XML file {file_path}: {e}")
        except Exception as e:
            raise XMLParsingError(f"Error processing XML file {file_path}: {e}")
    
    def parse_content(self, xml_content: str) -> List[Dict[str, Any]]:
        """
        Parse XML content string and return list of record definitions
        
        Args:
            xml_content: XML content as string
            
        Returns:
            List of record dictionaries
        """
        try:
            root = ET.fromstring(xml_content)
            return self._parse_data_element(root)
            
        except ET.ParseError as e:
            raise XMLParsingError(f"Failed to parse XML content: {e}")
        except Exception as e:
            raise XMLParsingError(f"Error processing XML content: {e}")
    
    def _parse_data_element(self, root: ET.Element) -> List[Dict[str, Any]]:
        """Parse the root data element and extract records"""
        records = []
        
        # Handle both <data> root and direct records
        if root.tag == 'data':
            elements = root
        else:
            elements = [root]
            
        for element in elements:
            if element.tag == 'record':
                record = self._parse_record_element(element)
                if record:
                    records.append(record)
            elif element.tag == 'data':
                # Nested data elements
                nested_records = self._parse_data_element(element)
                records.extend(nested_records)
                
        return records
    
    def _parse_record_element(self, record_elem: ET.Element) -> Optional[Dict[str, Any]]:
        """Parse a single record element"""
        # Get record attributes
        model = record_elem.get('model')
        xml_id = record_elem.get('id')
        
        if not model:
            self.logger.warning("Record element missing 'model' attribute, skipping")
            return None
            
        record = {
            'model': model,
            'xml_id': xml_id,
            'values': {},
            'context': record_elem.get('context', '{}'),
            'noupdate': record_elem.get('noupdate', '0') == '1'
        }
        
        # Parse field elements
        for field_elem in record_elem:
            field_name = field_elem.get('name')
            if not field_name:
                continue
                
            field_value = self._parse_field_element(field_elem)
            record['values'][field_name] = field_value
            
        return record
    
    def _parse_field_element(self, field_elem: ET.Element) -> Any:
        """Parse a field element and return its value"""
        field_type = field_elem.tag
        
        if field_type == 'field':
            # Regular field
            eval_attr = field_elem.get('eval')
            ref_attr = field_elem.get('ref')
            
            if eval_attr:
                # Python expression to evaluate
                return {'type': 'eval', 'value': eval_attr}
            elif ref_attr:
                # Reference to another record
                return {'type': 'ref', 'value': ref_attr}
            else:
                # Simple text value
                return {'type': 'text', 'value': field_elem.text or ''}
                
        elif field_type == 'value':
            # Value element (for One2many/Many2many)
            return {'type': 'value', 'value': field_elem.text or ''}
            
        else:
            # Unknown field type, treat as text
            return {'type': 'text', 'value': field_elem.text or ''}
    
    def validate_xml_structure(self, file_path: str) -> bool:
        """
        Validate XML file structure without full parsing
        
        Args:
            file_path: Path to XML file
            
        Returns:
            True if structure is valid
        """
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # Check if root is 'data' or contains record elements
            if root.tag == 'data':
                return True
            elif any(child.tag == 'record' for child in root):
                return True
            else:
                return False
                
        except ET.ParseError:
            return False
        except Exception:
            return False
