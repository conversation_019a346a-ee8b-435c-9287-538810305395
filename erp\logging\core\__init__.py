"""
Core Logging Components
Core system components for the ERP logging system
"""

# Core system components
from .core import (
    LoggingSystem,
    ComponentRegistry,
    LoggerManager,
    get_logging_system,
)

# Main facade interface
from .facade import (
    LoggingFacade,
    get_logging_facade,
    initialize_logging,
    get_logger,
    start_performance_monitoring,
    stop_performance_monitoring,
    record_request,
    operation_context,
    quiet_operation,
    get_system_status,
    shutdown_logging,

    log_performance,
    log_structured,
    LogContext,
    log_method_calls,
    log_database_operations,
    log_api_calls,
    log_security_events,
)

# Factory system
from .factories import (
    ComponentFactoryRegistry,
    FormatterFactory,
    FilterFactory,
    HandlerFactory,
    LoggerFactory,
    get_factory_registry,
)

__all__ = [
    # Core system
    'LoggingSystem',
    'ComponentRegistry',
    'LoggerManager',
    'get_logging_system',
    
    # Main facade interface
    'LoggingFacade',
    'get_logging_facade',
    'initialize_logging',
    'get_logger',
    'start_performance_monitoring',
    'stop_performance_monitoring',
    'record_request',
    'operation_context',
    'quiet_operation',
    'get_system_status',
    'shutdown_logging',

    'log_performance',
    'log_structured',
    'LogContext',
    'log_method_calls',
    'log_database_operations',
    'log_api_calls',
    'log_security_events',
    
    # Factory system
    'ComponentFactoryRegistry',
    'FormatterFactory',
    'FilterFactory',
    'HandlerFactory',
    'LoggerFactory',
    'get_factory_registry',
]
