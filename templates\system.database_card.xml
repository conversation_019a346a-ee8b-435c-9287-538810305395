<?xml version="1.0" encoding="utf-8"?>
<templates id="template_system_database_card" xml:space="preserve">

    <!-- Database Card Component -->
    <t t-name="system.database_card">
        <div class="group relative bg-white border border-gray-200 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-blue-200" t-att-data-dbname="db['name']">
            <!-- Subtle gradient overlay on hover -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-50/0 to-indigo-50/0 group-hover:from-blue-50/20 group-hover:to-indigo-50/10 transition-all duration-300 pointer-events-none"></div>

            <!-- Top accent line -->
            <div class="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-indigo-500 opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

            <!-- Card content -->
            <div class="relative p-4">
                <!-- Database Header -->
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-3 flex-1 min-w-0">
                        <!-- Compact database icon -->
                        <div class="relative">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white text-sm shadow-sm group-hover:shadow-md transition-all duration-300">
                                <i class="fas fa-database"></i>
                            </div>
                            <!-- Compact status indicator -->
                            <div t-att-class="'absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full border border-white shadow-sm ' + ('bg-green-500' if db['init_status'] == 'ready' else 'bg-yellow-500')"></div>
                        </div>

                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2 mb-1">
                                <h3 class="text-lg font-semibold text-gray-900 truncate group-hover:text-blue-700 transition-colors duration-300" t-esc="db['name']"/>
                                <!-- Inline status badge -->
                                <span t-att-class="'inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium ' + ('bg-green-100 text-green-700' if db['init_status'] == 'ready' else 'bg-yellow-100 text-yellow-700')">
                                    <i t-att-class="'fas mr-1 text-xs ' + db['init_icon']"></i>
                                    <span t-esc="'Ready' if db['init_status'] == 'ready' else 'Setup'"/>
                                </span>
                            </div>
                            <p class="text-xs text-gray-500 truncate" t-esc="db['encoding'] + ' • ' + db['owner']"/>
                        </div>
                    </div>

                    <!-- Delete Button (Dev Mode Only) -->
                    <t t-if="is_development_mode">
                        <button class="p-1.5 text-red-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 opacity-0 group-hover:opacity-100"
                                onclick="event.stopPropagation(); deleteDatabase(this.dataset.dbname)"
                                t-att-data-dbname="db['name']"
                                title="Delete Database (Dev Mode)">
                            <i class="fas fa-trash text-xs"></i>
                        </button>
                    </t>
                </div>

                <!-- Database Statistics Table -->
                <div class="mb-4 bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                    <table class="w-full text-xs">
                        <tbody class="divide-y divide-gray-200">
                            <tr class="hover:bg-gray-100 transition-colors duration-200">
                                <td class="px-3 py-2 font-medium text-gray-600 flex items-center">
                                    <i class="fas fa-hdd text-gray-400 mr-2 w-3"></i>
                                    Size
                                </td>
                                <td class="px-3 py-2 text-gray-900 font-medium text-right" t-esc="db['size']"></td>
                            </tr>
                            <tr class="hover:bg-gray-100 transition-colors duration-200">
                                <td class="px-3 py-2 font-medium text-gray-600 flex items-center">
                                    <i class="fas fa-table text-gray-400 mr-2 w-3"></i>
                                    Tables
                                </td>
                                <td class="px-3 py-2 text-gray-900 font-medium text-right" t-esc="db['table_count']"></td>
                            </tr>
                            <tr class="hover:bg-gray-100 transition-colors duration-200">
                                <td class="px-3 py-2 font-medium text-gray-600 flex items-center">
                                    <i class="fas fa-plug text-gray-400 mr-2 w-3"></i>
                                    Connections
                                </td>
                                <td class="px-3 py-2 text-gray-900 font-medium text-right" t-esc="db['active_connections']"></td>
                            </tr>
                            <tr class="hover:bg-gray-100 transition-colors duration-200">
                                <td class="px-3 py-2 font-medium text-gray-600 flex items-center">
                                    <i class="fas fa-calendar text-gray-400 mr-2 w-3"></i>
                                    Created
                                </td>
                                <td class="px-3 py-2 text-gray-900 font-medium text-right" t-esc="db['created_display'] or 'Unknown'"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Compact Registry Status -->
                <t t-if="db['has_memory_registry']">
                    <div class="mb-3 p-2 bg-indigo-50 border border-indigo-100 rounded-lg">
                        <div class="flex items-center space-x-2">
                            <div class="w-5 h-5 bg-indigo-500 rounded flex items-center justify-center text-white text-xs">
                                <i class="fas fa-memory"></i>
                            </div>
                            <span class="text-xs font-medium text-indigo-800">Memory Registry</span>
                            <div class="flex-1"></div>
                            <div class="flex space-x-0.5">
                                <div class="w-1.5 h-1.5 bg-indigo-500 rounded-full animate-pulse"></div>
                                <div class="w-1.5 h-1.5 bg-indigo-400 rounded-full animate-pulse [animation-delay:0.2s]"></div>
                                <div class="w-1.5 h-1.5 bg-indigo-300 rounded-full animate-pulse [animation-delay:0.4s]"></div>
                            </div>
                        </div>
                    </div>
                </t>

                <!-- Action Buttons - Horizontal Layout -->
                <div class="flex gap-2">
                    <!-- Primary Action Button -->
                    <t t-if="db['init_status'] == 'ready'">
                        <button class="flex-1 px-4 py-2.5 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-all duration-200 flex items-center justify-center space-x-2 shadow-sm hover:shadow-md"
                                onclick="connectToDatabase(this.dataset.dbname)"
                                t-att-data-dbname="db['name']">
                            <i class="fas fa-sign-in-alt text-sm"></i>
                            <span>Connect</span>
                        </button>
                    </t>
                    <t t-else="">
                        <button class="flex-1 px-4 py-2.5 bg-amber-600 text-white rounded-lg text-sm font-medium hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-1 transition-all duration-200 flex items-center justify-center space-x-2 shadow-sm hover:shadow-md"
                                onclick="initializeDatabase(this.dataset.dbname)"
                                t-att-data-dbname="db['name']">
                            <i class="fas fa-cog text-sm"></i>
                            <span>Initialize</span>
                        </button>
                    </t>

                    <!-- Secondary Action Buttons -->
                    <t t-if="db['init_status'] == 'ready'">
                        <!-- Backup Button -->
                        <button class="px-3 py-2.5 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1 transition-all duration-200 flex items-center justify-center shadow-sm hover:shadow-md"
                                onclick="backupDatabase(this.dataset.dbname)"
                                t-att-data-dbname="db['name']"
                                title="Backup Database">
                            <i class="fas fa-download text-sm"></i>
                        </button>

                        <!-- Settings Button -->
                        <button class="px-3 py-2.5 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1 transition-all duration-200 flex items-center justify-center shadow-sm hover:shadow-md"
                                onclick="manageDatabaseSettings(this.dataset.dbname)"
                                t-att-data-dbname="db['name']"
                                title="Database Settings">
                            <i class="fas fa-cog text-sm"></i>
                        </button>
                    </t>
                </div>
            </div>
        </div>
    </t>

</templates>
