"""
Controller mixins
Provides specific functionality that can be mixed into controllers
"""

from typing import Any, Dict, List, Optional, Union
from abc import ABC

from .base import BaseController, ControllerContextError
from ...logging import get_logger

logger = get_logger(__name__)


class RequestMixin:
    """Mixin for request handling functionality"""
    
    @property
    def request(self) -> Optional[Any]:
        """Get current request object"""
        if not hasattr(self, '_get_request'):
            raise ControllerContextError("RequestMixin requires BaseController")
        return self._get_request()
    
    @property
    def params(self) -> Dict[str, Any]:
        """Get request parameters (query params + form data)"""
        if not self.request:
            return {}
        
        params = {}
        
        # Add query parameters
        if hasattr(self.request, 'query_params'):
            params.update(dict(self.request.query_params))
        
        # Add form data if available
        if hasattr(self.request, 'state'):
            form_data = getattr(self.request.state, 'form_data', None)
            if form_data:
                params.update(dict(form_data))
        
        return params
    
    @property
    def headers(self) -> Dict[str, str]:
        """Get request headers"""
        if not self.request:
            return {}
        
        if hasattr(self.request, 'headers'):
            return dict(self.request.headers)
        
        return {}
    
    def get_param(self, key: str, default: Any = None) -> Any:
        """Get a specific parameter value"""
        return self.params.get(key, default)
    
    def get_header(self, key: str, default: Any = None) -> Any:
        """Get a specific header value (case-insensitive)"""
        if not self.request or not hasattr(self.request, 'headers'):
            return default
        
        return self.request.headers.get(key, default)
    
    def get_json_data(self) -> Optional[Dict[str, Any]]:
        """Get JSON data from request"""
        if not self.request:
            return None
        
        # Check request state first
        if hasattr(self.request, 'state'):
            json_data = getattr(self.request.state, 'json_data', None)
            if json_data:
                return json_data
        
        # Check request attribute
        return getattr(self.request, '_json_data', None)
    
    def get_form_data(self) -> Optional[Dict[str, Any]]:
        """Get form data from request"""
        if not self.request:
            return None
        
        # Check request state first
        if hasattr(self.request, 'state'):
            form_data = getattr(self.request.state, 'form_data', None)
            if form_data:
                return dict(form_data)
        
        # Check request attribute
        form_data = getattr(self.request, '_form_data', None)
        return dict(form_data) if form_data else None


class ResponseMixin:
    """Mixin for response generation functionality"""
    
    def json_response(self, data: Any, status_code: int = 200) -> Any:
        """Create a JSON response"""
        try:
            from fastapi.responses import JSONResponse
            return JSONResponse(content=data, status_code=status_code)
        except ImportError:
            # Fallback for non-FastAPI environments
            return {'data': data, 'status_code': status_code}
    
    def html_response(self, content: str, status_code: int = 200) -> Any:
        """Create an HTML response"""
        try:
            from fastapi.responses import HTMLResponse
            return HTMLResponse(content=content, status_code=status_code)
        except ImportError:
            # Fallback for non-FastAPI environments
            return {'content': content, 'content_type': 'text/html', 'status_code': status_code}
    
    def text_response(self, content: str, status_code: int = 200) -> Any:
        """Create a plain text response"""
        try:
            from fastapi.responses import PlainTextResponse
            return PlainTextResponse(content=content, status_code=status_code)
        except ImportError:
            # Fallback for non-FastAPI environments
            return {'content': content, 'content_type': 'text/plain', 'status_code': status_code}
    
    def redirect(self, url: str, status_code: int = 302) -> Any:
        """Create a redirect response"""
        try:
            from fastapi.responses import RedirectResponse
            return RedirectResponse(url=url, status_code=status_code)
        except ImportError:
            # Fallback for non-FastAPI environments
            return {'redirect_url': url, 'status_code': status_code}
    
    def error_response(self, message: str, status_code: int = 400, details: Optional[Dict] = None) -> Any:
        """Create an error response"""
        error_data = {'error': message}
        if details:
            error_data['details'] = details
        
        return self.json_response(error_data, status_code)


class TemplateMixin:
    """Mixin for template rendering functionality"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._template_manager = None
    
    @property
    def template_manager(self):
        """Get template manager (lazy loading)"""
        if self._template_manager is None:
            try:
                from ...templates.manager import get_template_manager
                self._template_manager = get_template_manager()
            except ImportError:
                logger.warning("Template manager not available")
                self._template_manager = None
        return self._template_manager
    
    async def render_template(self, template_name: str, context: Optional[Dict[str, Any]] = None) -> Any:
        """Render a template with the given context"""
        if not self.template_manager:
            raise ControllerContextError("Template manager not available")
        
        if context is None:
            context = {}
        
        try:
            html_content = await self.template_manager.render_template_async(template_name, context)
            return self.html_response(html_content)
        except Exception as e:
            logger.error(f"Error rendering template {template_name}: {e}")
            return self.error_response(f"Template rendering error: {str(e)}", 500)


class DatabaseMixin:
    """Mixin for database functionality"""
    
    @property
    def env(self) -> Optional[Any]:
        """Get current environment"""
        if not hasattr(self, '_get_env'):
            raise ControllerContextError("DatabaseMixin requires BaseController")
        
        env = self._get_env()
        if env:
            return env
        
        # Try to get from context manager
        try:
            from ...context import ContextManager
            return ContextManager.get_environment()
        except ImportError:
            return None
    
    def get_current_user(self) -> Optional[Any]:
        """Get current user from environment"""
        if self.env and hasattr(self.env, 'uid'):
            return self.env.uid
        return None
    
    def get_current_database(self) -> Optional[str]:
        """Get current database name"""
        if self.env and hasattr(self.env, 'cr') and hasattr(self.env.cr, 'db_name'):
            return self.env.cr.db_name
        return None
    
    async def get_database_list(self) -> List[Dict[str, Any]]:
        """Get list of available databases"""
        try:
            from ...routes.database import get_database_list
            return await get_database_list()
        except ImportError:
            logger.warning("Database list functionality not available")
            return []
    
    def check_database_access(self, db_name: str) -> bool:
        """Check if database is accessible"""
        try:
            from ...database.memory import DatabaseFilterProcessor
            from ...config import config
            return DatabaseFilterProcessor.check_database_matches_filter(db_name, config.db_filter)
        except ImportError:
            logger.warning("Database access check not available")
            return True


class AuthMixin:
    """Mixin for authentication functionality"""
    
    def get_current_user_info(self) -> Optional[Dict[str, Any]]:
        """Get current user information from request"""
        if not hasattr(self, '_get_request'):
            raise ControllerContextError("AuthMixin requires BaseController")
        
        request = self._get_request()
        if not request:
            return None
        
        # Try to get user from request state
        if hasattr(request, 'state') and hasattr(request.state, 'user'):
            return request.state.user
        
        # Try to get from request attribute
        return getattr(request, '_auth_user', None)
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        return self.get_current_user_info() is not None
    
    def is_admin(self) -> bool:
        """Check if current user is admin"""
        user_info = self.get_current_user_info()
        if not user_info:
            return False
        return user_info.get('is_admin', False)
    
    def require_authentication(self) -> None:
        """Require authentication, raise error if not authenticated"""
        if not self.is_authenticated():
            raise ControllerContextError("Authentication required")
    
    def require_admin(self) -> None:
        """Require admin privileges, raise error if not admin"""
        self.require_authentication()
        if not self.is_admin():
            raise ControllerContextError("Admin privileges required")
    
    def get_database_from_request(self) -> Optional[str]:
        """Get database name from request"""
        if not hasattr(self, '_get_request'):
            return None
        
        request = self._get_request()
        if not request:
            return None
        
        # Check query parameter
        if hasattr(request, 'query_params'):
            db_name = request.query_params.get('db')
            if db_name:
                return db_name
        
        # Check cookies
        if hasattr(request, 'cookies'):
            db_name = request.cookies.get('erp_database')
            if db_name:
                return db_name
        
        return None


class ValidationMixin:
    """Mixin for request validation functionality"""
    
    def validate_required_params(self, required: List[str]) -> None:
        """Validate that required parameters are present"""
        if not hasattr(self, 'params'):
            raise ControllerContextError("ValidationMixin requires RequestMixin")
        
        missing = [param for param in required if param not in self.params]
        if missing:
            raise ControllerContextError(f"Missing required parameters: {missing}")
    
    def validate_param_types(self, type_map: Dict[str, type]) -> None:
        """Validate parameter types"""
        if not hasattr(self, 'params'):
            raise ControllerContextError("ValidationMixin requires RequestMixin")
        
        for param, expected_type in type_map.items():
            if param in self.params:
                value = self.params[param]
                if not isinstance(value, expected_type):
                    raise ControllerContextError(
                        f"Parameter {param} must be of type {expected_type.__name__}, got {type(value).__name__}"
                    )
