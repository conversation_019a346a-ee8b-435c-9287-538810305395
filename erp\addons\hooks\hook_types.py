"""
Hook types and enumerations

This module defines the types of hooks available in the addon lifecycle system.
"""
from enum import Enum


class HookType(Enum):
    """Types of addon lifecycle hooks"""
    PRE_INSTALL = "pre_install"
    POST_INSTALL = "post_install"
    PRE_UNINSTALL = "pre_uninstall"
    POST_UNINSTALL = "post_uninstall"
    PRE_UPGRADE = "pre_upgrade"
    POST_UPGRADE = "post_upgrade"
