"""
Transaction middleware for automatic database transaction management
"""
import logging
from fastapi import Request
from typing import Callable, Any
from ...context import Context<PERSON>anager
from ..core.responses import handle_generic_error

logger = logging.getLogger(__name__)


class TransactionMiddleware:
    """
    Automatically manages database transactions for each request.
    
    This middleware ensures that:
    1. Each request that needs database access runs within a transaction
    2. Transactions are automatically committed on successful completion
    3. Transactions are automatically rolled back on exceptions
    4. Non-database requests are not affected
    """

    @staticmethod
    def should_skip(path: str) -> bool:
        """Check if the given path should skip transaction middleware."""
        # Import here to avoid circular imports
        from .database import RouteClassifier
        
        # Skip transaction middleware for system routes that don't need database
        needs_db = RouteClassifier.needs_database_middleware(path)
        
        if not needs_db:
            logger.debug(f"⏭️ Skipping transaction middleware for system path: {path}")
            return True
        else:
            logger.debug(f"🔄 Transaction middleware required for path: {path}")
            return False

    @staticmethod
    async def process_request(request: Request, call_next: Callable) -> Any:
        """
        Process request with automatic transaction management.
        
        This middleware:
        1. Checks if the request needs database access
        2. Gets the environment from the request context
        3. Begins a transaction
        4. Executes the request
        5. Commits on success or rolls back on failure
        """
        # Skip transaction middleware for system routes
        if TransactionMiddleware.should_skip(request.url.path):
            return await call_next(request)

        # Get environment from request state (set by EnvironmentMiddleware)
        env = getattr(request.state, 'env', None)
        if not env:
            logger.warning(f"No environment found in request state for {request.url.path}")
            # Continue without transaction management
            return await call_next(request)

        # Check if environment has a database cursor
        if not hasattr(env, 'cr') or not env.cr:
            logger.warning(f"No database cursor found in environment for {request.url.path}")
            return await call_next(request)

        logger.debug(f"🔄 Starting transaction for request: {request.method} {request.url.path}")
        
        try:
            # Begin transaction
            async with env.cr._get_connection():
                await env.cr.begin()
                logger.debug(f"✅ Transaction started for {request.url.path}")
                
                try:
                    # Execute the request within the transaction
                    response = await call_next(request)
                    
                    # Commit transaction on successful completion
                    await env.cr.commit()
                    logger.debug(f"✅ Transaction committed for {request.url.path}")
                    
                    return response
                    
                except Exception as e:
                    # Rollback transaction on any exception
                    await env.cr.rollback()
                    logger.warning(f"🔄 Transaction rolled back for {request.url.path}: {e}")
                    raise
                    
        except Exception as e:
            logger.error(f"❌ Transaction middleware error for {request.url.path}: {e}")
            raise handle_generic_error(e)


# Convenience function for middleware registration
async def transaction_middleware(request: Request, call_next: Callable) -> Any:
    """Convenience function for transaction middleware registration."""
    return await TransactionMiddleware.process_request(request, call_next)
