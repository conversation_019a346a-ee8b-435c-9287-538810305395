"""
Cluster ID management
"""
import os
import socket
import uuid


class ClusterIdManager:
    """Manages cluster and node ID generation"""
    
    def __init__(self):
        self.cluster_id = None
        self.node_id = None
    
    def generate_cluster_ids(self):
        """Generate unique cluster and node identifiers"""
        # Generate or load cluster ID
        cluster_id_file = "cluster.id"
        if os.path.exists(cluster_id_file):
            with open(cluster_id_file, 'r') as f:
                self.cluster_id = f.read().strip()
        else:
            self.cluster_id = str(uuid.uuid4())
            with open(cluster_id_file, 'w') as f:
                f.write(self.cluster_id)
        
        # Generate node ID based on hostname and process
        hostname = socket.gethostname()
        pid = os.getpid()
        self.node_id = f"{hostname}-{pid}"
        
        return self.cluster_id, self.node_id
