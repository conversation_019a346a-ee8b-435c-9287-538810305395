"""
Template Filters - Data formatting and transformation filters
"""
import html
import re
from datetime import datetime, date
from decimal import Decimal
from typing import Any, Dict, Callable, List, Optional, Union
from .exceptions import TemplateError


class FilterRegistry:
    """Registry for template filters"""
    
    _filters: Dict[str, Callable] = {}
    
    @classmethod
    def register(cls, name: str, filter_func: Callable):
        """Register a filter function"""
        cls._filters[name] = filter_func
    
    @classmethod
    def apply_filter(cls, name: str, value: Any, *args, **kwargs) -> Any:
        """Apply a filter to a value"""
        if name not in cls._filters:
            raise TemplateError(f"Unknown filter: {name}")
        
        try:
            return cls._filters[name](value, *args, **kwargs)
        except Exception as e:
            raise TemplateError(f"Error applying filter '{name}': {e}")
    
    @classmethod
    def get_available_filters(cls) -> List[str]:
        """Get list of available filter names"""
        return list(cls._filters.keys())


# Text Filters
def filter_upper(value: Any) -> str:
    """Convert text to uppercase"""
    return str(value).upper()


def filter_lower(value: Any) -> str:
    """Convert text to lowercase"""
    return str(value).lower()


def filter_title(value: Any) -> str:
    """Convert text to title case"""
    return str(value).title()


def filter_capitalize(value: Any) -> str:
    """Capitalize first letter"""
    return str(value).capitalize()


def filter_truncate(value: Any, length: int = 50, suffix: str = "...") -> str:
    """Truncate text to specified length"""
    text = str(value)
    if len(text) <= length:
        return text
    return text[:length - len(suffix)] + suffix


def filter_wordwrap(value: Any, width: int = 80) -> str:
    """Wrap text to specified width"""
    import textwrap
    return textwrap.fill(str(value), width=width)


def filter_escape(value: Any) -> str:
    """HTML escape the value"""
    return html.escape(str(value))


def filter_striptags(value: Any) -> str:
    """Remove HTML tags from text"""
    text = str(value)
    return re.sub(r'<[^>]+>', '', text)


def filter_slugify(value: Any) -> str:
    """Convert text to URL-friendly slug"""
    text = str(value).lower()
    text = re.sub(r'[^\w\s-]', '', text)
    text = re.sub(r'[-\s]+', '-', text)
    return text.strip('-')


# Number Filters
def filter_abs(value: Any) -> Union[int, float]:
    """Return absolute value"""
    return abs(float(value))


def filter_round(value: Any, precision: int = 0) -> Union[int, float]:
    """Round number to specified precision"""
    if precision == 0:
        return round(float(value))
    return round(float(value), precision)


def filter_int(value: Any) -> int:
    """Convert to integer"""
    return int(float(value))


def filter_float(value: Any) -> float:
    """Convert to float"""
    return float(value)


def filter_currency(value: Any, symbol: str = "$", precision: int = 2) -> str:
    """Format as currency"""
    num = float(value)
    return f"{symbol}{num:,.{precision}f}"


def filter_percentage(value: Any, precision: int = 1) -> str:
    """Format as percentage"""
    num = float(value) * 100
    return f"{num:.{precision}f}%"


def filter_filesizeformat(value: Any) -> str:
    """Format file size in human readable format"""
    try:
        bytes_val = float(value)
    except (ValueError, TypeError):
        return str(value)
    
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_val < 1024.0:
            return f"{bytes_val:.1f} {unit}"
        bytes_val /= 1024.0
    return f"{bytes_val:.1f} PB"


# Date/Time Filters
def filter_date(value: Any, format_str: str = "%Y-%m-%d") -> str:
    """Format date"""
    if isinstance(value, str):
        # Try to parse string as date
        try:
            value = datetime.fromisoformat(value.replace('Z', '+00:00'))
        except ValueError:
            return str(value)
    
    if isinstance(value, (datetime, date)):
        return value.strftime(format_str)
    
    return str(value)


def filter_time(value: Any, format_str: str = "%H:%M:%S") -> str:
    """Format time"""
    if isinstance(value, str):
        try:
            value = datetime.fromisoformat(value.replace('Z', '+00:00'))
        except ValueError:
            return str(value)
    
    if isinstance(value, datetime):
        return value.strftime(format_str)
    
    return str(value)


def filter_datetime(value: Any, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """Format datetime"""
    return filter_date(value, format_str)


def filter_timesince(value: Any) -> str:
    """Time since given date"""
    if isinstance(value, str):
        try:
            value = datetime.fromisoformat(value.replace('Z', '+00:00'))
        except ValueError:
            return str(value)
    
    if not isinstance(value, datetime):
        return str(value)
    
    now = datetime.now()
    if value.tzinfo:
        from datetime import timezone
        now = datetime.now(timezone.utc)
    
    diff = now - value
    
    if diff.days > 0:
        return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
    
    hours = diff.seconds // 3600
    if hours > 0:
        return f"{hours} hour{'s' if hours != 1 else ''} ago"
    
    minutes = diff.seconds // 60
    if minutes > 0:
        return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
    
    return "just now"


# List/Collection Filters
def filter_length(value: Any) -> int:
    """Get length of collection"""
    try:
        return len(value)
    except TypeError:
        return 0


def filter_first(value: Any) -> Any:
    """Get first item from collection"""
    try:
        return next(iter(value))
    except (TypeError, StopIteration):
        return None


def filter_last(value: Any) -> Any:
    """Get last item from collection"""
    try:
        if hasattr(value, '__getitem__'):
            return value[-1]
        return list(value)[-1]
    except (TypeError, IndexError):
        return None


def filter_join(value: Any, separator: str = ", ") -> str:
    """Join collection items with separator"""
    try:
        return separator.join(str(item) for item in value)
    except TypeError:
        return str(value)


def filter_sort(value: Any, key: Optional[str] = None, reverse: bool = False) -> List[Any]:
    """Sort collection"""
    try:
        items = list(value)
        if key:
            # Sort by attribute/key
            def get_key(item):
                if hasattr(item, key):
                    return getattr(item, key)
                elif isinstance(item, dict) and key in item:
                    return item[key]
                return item
            return sorted(items, key=get_key, reverse=reverse)
        else:
            return sorted(items, reverse=reverse)
    except (TypeError, AttributeError):
        return list(value) if hasattr(value, '__iter__') else [value]


def filter_unique(value: Any) -> List[Any]:
    """Remove duplicates while preserving order"""
    try:
        seen = set()
        result = []
        for item in value:
            if item not in seen:
                seen.add(item)
                result.append(item)
        return result
    except (TypeError, AttributeError):
        return list(value) if hasattr(value, '__iter__') else [value]


# Utility Filters
def filter_default(value: Any, default_value: Any = "") -> Any:
    """Return default value if value is falsy"""
    return value if value else default_value


def filter_default_if_none(value: Any, default_value: Any = "") -> Any:
    """Return default value if value is None"""
    return value if value is not None else default_value


def filter_yesno(value: Any, yes: str = "Yes", no: str = "No", maybe: str = "Maybe") -> str:
    """Convert boolean to yes/no string"""
    if value is None:
        return maybe
    return yes if value else no


# Register all filters
FilterRegistry.register('upper', filter_upper)
FilterRegistry.register('lower', filter_lower)
FilterRegistry.register('title', filter_title)
FilterRegistry.register('capitalize', filter_capitalize)
FilterRegistry.register('truncate', filter_truncate)
FilterRegistry.register('wordwrap', filter_wordwrap)
FilterRegistry.register('escape', filter_escape)
FilterRegistry.register('striptags', filter_striptags)
FilterRegistry.register('slugify', filter_slugify)

FilterRegistry.register('abs', filter_abs)
FilterRegistry.register('round', filter_round)
FilterRegistry.register('int', filter_int)
FilterRegistry.register('float', filter_float)
FilterRegistry.register('currency', filter_currency)
FilterRegistry.register('percentage', filter_percentage)
FilterRegistry.register('filesizeformat', filter_filesizeformat)

FilterRegistry.register('date', filter_date)
FilterRegistry.register('time', filter_time)
FilterRegistry.register('datetime', filter_datetime)
FilterRegistry.register('timesince', filter_timesince)

FilterRegistry.register('length', filter_length)
FilterRegistry.register('first', filter_first)
FilterRegistry.register('last', filter_last)
FilterRegistry.register('join', filter_join)
FilterRegistry.register('sort', filter_sort)
FilterRegistry.register('unique', filter_unique)

FilterRegistry.register('default', filter_default)
FilterRegistry.register('default_if_none', filter_default_if_none)
FilterRegistry.register('yesno', filter_yesno)
