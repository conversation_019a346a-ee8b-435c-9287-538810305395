"""
Test suite for AddonManager functionality

This module tests:
- AddonManager initialization and configuration
- Addon load order management
- Addon lifecycle operations
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from erp.database.memory import AddonManager


class TestAddonManager:
    """Test AddonManager functionality"""
    
    @pytest.mark.asyncio
    async def test_addon_manager_initialization(self):
        """Test AddonManager initialization"""
        manager = AddonManager("test_db")

        assert manager.db_name == "test_db"
        assert hasattr(manager, 'addon_load_order')
        assert manager.addon_load_order == []
    
    @pytest.mark.asyncio
    async def test_set_addon_load_order(self):
        """Test setting addon load order"""
        manager = AddonManager("test_db")
        
        addon_order = ["base", "addon1", "addon2"]
        await manager.set_addon_load_order(addon_order)
        
        result = await manager.get_addon_load_order()
        assert result == addon_order
    
    @pytest.mark.asyncio
    async def test_get_addon_load_order_empty(self):
        """Test getting addon load order when empty"""
        manager = AddonManager("test_db")
        
        result = await manager.get_addon_load_order()
        assert result == []
    
    @pytest.mark.asyncio
    async def test_addon_load_order_persistence(self):
        """Test that addon load order persists across manager instances"""
        manager1 = AddonManager("test_db")
        addon_order = ["base", "addon1", "addon2"]
        await manager1.set_addon_load_order(addon_order)
        
        # Create new manager instance
        manager2 = AddonManager("test_db")
        result = await manager2.get_addon_load_order()
        assert result == addon_order
    
    @pytest.mark.asyncio
    async def test_addon_load_order_update(self):
        """Test updating addon load order"""
        manager = AddonManager("test_db")
        
        # Set initial order
        initial_order = ["base", "addon1"]
        await manager.set_addon_load_order(initial_order)
        
        # Update order
        updated_order = ["base", "addon1", "addon2", "addon3"]
        await manager.set_addon_load_order(updated_order)
        
        result = await manager.get_addon_load_order()
        assert result == updated_order
        assert len(result) == 4
    
    @pytest.mark.asyncio
    async def test_addon_load_order_empty_list(self):
        """Test setting empty addon load order"""
        manager = AddonManager("test_db")
        
        # Set some initial order
        await manager.set_addon_load_order(["base", "addon1"])
        
        # Clear the order
        await manager.set_addon_load_order([])
        
        result = await manager.get_addon_load_order()
        assert result == []
    
    @pytest.mark.asyncio
    async def test_addon_load_order_duplicate_handling(self):
        """Test handling of duplicate addons in load order"""
        manager = AddonManager("test_db")
        
        # Set order with duplicates
        addon_order = ["base", "addon1", "base", "addon2"]
        await manager.set_addon_load_order(addon_order)
        
        result = await manager.get_addon_load_order()
        # Should preserve the order as set (implementation may or may not deduplicate)
        assert result == addon_order
