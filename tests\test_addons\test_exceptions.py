"""
Test suite for addon exceptions module

This module tests:
- Exception class hierarchy
- Exception message handling
- Exception inheritance
- Custom exception attributes
"""
import pytest

from erp.addons.exceptions import (
    AddonError,
    DependencyError,
    CircularDependencyError,
    MissingDependencyError,
    AddonInstallationError,
    AddonUninstallationError,
    AddonUpgradeError,
    AddonManifestError,
    AddonNotFoundError,
    AddonStateError,
    AddonRegistryError,
    AddonHookError,
    AddonLifecycleError,
)


class TestAddonExceptions:
    """Test addon exception classes"""
    
    def test_addon_error_base_class(self):
        """Test AddonError base class"""
        error = AddonError("Test error message")
        assert str(error) == "Test error message"
        assert isinstance(error, Exception)
    
    def test_dependency_error(self):
        """Test DependencyError"""
        error = DependencyError("Dependency issue")
        assert str(error) == "Dependency issue"
        assert isinstance(error, AddonError)
    
    def test_circular_dependency_error(self):
        """Test CircularDependencyError"""
        error = CircularDependencyError("Circular dependency detected")
        assert str(error) == "Circular dependency detected"
        assert isinstance(error, DependencyError)
        assert isinstance(error, AddonError)
    
    def test_missing_dependency_error(self):
        """Test MissingDependencyError"""
        error = MissingDependencyError("Missing dependency")
        assert str(error) == "Missing dependency"
        assert isinstance(error, DependencyError)
        assert isinstance(error, AddonError)
    
    def test_addon_installation_error(self):
        """Test AddonInstallationError"""
        error = AddonInstallationError("Installation failed")
        assert str(error) == "Installation failed"
        assert isinstance(error, AddonError)
    
    def test_addon_uninstallation_error(self):
        """Test AddonUninstallationError"""
        error = AddonUninstallationError("Uninstallation failed")
        assert str(error) == "Uninstallation failed"
        assert isinstance(error, AddonError)
    
    def test_addon_upgrade_error(self):
        """Test AddonUpgradeError"""
        error = AddonUpgradeError("Upgrade failed")
        assert str(error) == "Upgrade failed"
        assert isinstance(error, AddonError)
    
    def test_addon_manifest_error(self):
        """Test AddonManifestError"""
        error = AddonManifestError("Manifest error")
        assert str(error) == "Manifest error"
        assert isinstance(error, AddonError)
    
    def test_addon_not_found_error(self):
        """Test AddonNotFoundError"""
        error = AddonNotFoundError("Addon not found")
        assert str(error) == "Addon not found"
        assert isinstance(error, AddonError)
    
    def test_addon_state_error(self):
        """Test AddonStateError"""
        error = AddonStateError("State error")
        assert str(error) == "State error"
        assert isinstance(error, AddonError)
    
    def test_addon_registry_error(self):
        """Test AddonRegistryError"""
        error = AddonRegistryError("Registry error")
        assert str(error) == "Registry error"
        assert isinstance(error, AddonError)
    
    def test_addon_hook_error(self):
        """Test AddonHookError"""
        error = AddonHookError("Hook error")
        assert str(error) == "Hook error"
        assert isinstance(error, AddonError)
    
    def test_addon_lifecycle_error(self):
        """Test AddonLifecycleError"""
        error = AddonLifecycleError("Lifecycle error")
        assert str(error) == "Lifecycle error"
        assert isinstance(error, AddonError)
    
    def test_exception_with_custom_attributes(self):
        """Test exceptions with custom attributes"""
        error = AddonInstallationError("Installation failed")
        error.addon_name = "test_addon"
        error.error_code = "INSTALL_001"
        
        assert error.addon_name == "test_addon"
        assert error.error_code == "INSTALL_001"
        assert str(error) == "Installation failed"
    
    def test_exception_chaining(self):
        """Test exception chaining"""
        original_error = ValueError("Original error")
        try:
            raise AddonError("Addon error") from original_error
        except AddonError as addon_error:
            assert str(addon_error) == "Addon error"
            assert addon_error.__cause__ == original_error
    
    def test_all_exceptions_importable(self):
        """Test that all exceptions can be imported from the package"""
        from erp.addons.exceptions import __all__
        
        expected_exceptions = [
            'AddonError',
            'DependencyError',
            'CircularDependencyError',
            'MissingDependencyError',
            'AddonInstallationError',
            'AddonUninstallationError',
            'AddonUpgradeError',
            'AddonManifestError',
            'AddonNotFoundError',
            'AddonStateError',
            'AddonRegistryError',
            'AddonHookError',
            'AddonLifecycleError',
        ]
        
        for exception_name in expected_exceptions:
            assert exception_name in __all__
    
    def test_exception_inheritance_hierarchy(self):
        """Test the exception inheritance hierarchy"""
        # Test that all exceptions inherit from AddonError
        exceptions_to_test = [
            DependencyError,
            AddonInstallationError,
            AddonUninstallationError,
            AddonUpgradeError,
            AddonManifestError,
            AddonNotFoundError,
            AddonStateError,
            AddonRegistryError,
            AddonHookError,
            AddonLifecycleError,
        ]
        
        for exception_class in exceptions_to_test:
            assert issubclass(exception_class, AddonError)
        
        # Test specific inheritance
        assert issubclass(CircularDependencyError, DependencyError)
        assert issubclass(MissingDependencyError, DependencyError)
