"""
Middleware setup for FastAPI server
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from ..config import config
from ..utils.middleware import (
    timing_middleware, error_handling_middleware, logging_middleware,
    database_middleware, environment_middleware, transaction_middleware
)


class MiddlewareSetup:
    """Handles middleware configuration for FastAPI"""
    
    def setup_middleware(self, app: FastAPI):
        """Setup FastAPI middleware"""
        # CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=config.cors_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Custom middleware (order matters - execution is in reverse order of registration)
        # Note: FastAPI middleware executes in reverse order of registration
        # Execution order: database -> environment -> transaction -> logging -> error_handling -> timing
        app.middleware("http")(timing_middleware)        # Executes last
        app.middleware("http")(error_handling_middleware)
        app.middleware("http")(logging_middleware)
        app.middleware("http")(transaction_middleware)   # Executes after environment, before logging
        app.middleware("http")(environment_middleware)   # Executes after database, before transaction
        app.middleware("http")(database_middleware)      # Executes first
