"""
Test suite for modular addon utilities

This module tests:
- Module registry utilities
- Discovery utilities
- Helper functions
- Constants and enumerations
- Utility integration
"""
import pytest
from unittest.mock import MagicMock, AsyncMock, patch
from pathlib import Path

from erp.addons.utils import (
    # Module registry utilities
    register_addon_in_module_table,
    unregister_addon_from_module_table,
    update_addon_state_in_module_table,
    read_manifest_file,
    auto_register_addon_from_manifest,
    
    # Discovery utilities
    discover_addons_in_path,
    discover_addons_in_multiple_paths,
    validate_addon_structure,
    get_addon_dependencies,
    build_dependency_graph,
    detect_circular_dependencies,
    get_installation_order,
    find_missing_dependencies,
    
    # Helper functions
    TimingContext,
    retry_async_operation,
    format_duration,
    safe_get_nested_value,
    merge_dictionaries,
    validate_addon_name,
    normalize_addon_name,
    create_error_response,
    create_success_response,
    transaction_context,
    chunk_list,
    flatten_list,
    
    # Constants
    AddonState,
    AddonCategory,
    ManifestKeys,
    DefaultValues,
    FileNames,
    DatabaseTables,
    ModuleStates,
    HookPriorities,
    ValidationLevels,
    LogMessages,
    ErrorCodes
)


class TestModuleRegistryUtilities:
    """Test module registry utility functions"""
    
    @pytest.mark.asyncio
    async def test_register_addon_in_module_table(self):
        """Test addon registration in module table"""
        mock_db = MagicMock()
        
        with patch('erp.addons.utils.register_addon_in_module_table', return_value=True) as mock_register:
            result = await register_addon_in_module_table(mock_db, "test_addon", {"version": "1.0.0"})
            assert result is True
            mock_register.assert_called_once_with(mock_db, "test_addon", {"version": "1.0.0"})
    
    @pytest.mark.asyncio
    async def test_unregister_addon_from_module_table(self):
        """Test addon unregistration from module table"""
        mock_db = MagicMock()
        
        with patch('erp.addons.utils.unregister_addon_from_module_table', return_value=True) as mock_unregister:
            result = await unregister_addon_from_module_table(mock_db, "test_addon")
            assert result is True
            mock_unregister.assert_called_once_with(mock_db, "test_addon")
    
    @pytest.mark.asyncio
    async def test_update_addon_state_in_module_table(self):
        """Test addon state update in module table"""
        mock_db = MagicMock()
        
        with patch('erp.addons.utils.update_addon_state_in_module_table', return_value=True) as mock_update:
            result = await update_addon_state_in_module_table(mock_db, "test_addon", "installed")
            assert result is True
            mock_update.assert_called_once_with(mock_db, "test_addon", "installed")
    
    def test_read_manifest_file(self):
        """Test manifest file reading"""
        with patch('erp.addons.utils.read_manifest_file', return_value={"name": "test_addon", "version": "1.0.0"}) as mock_read:
            result = read_manifest_file("/path/to/manifest.py")
            assert result["name"] == "test_addon"
            assert result["version"] == "1.0.0"
            mock_read.assert_called_once_with("/path/to/manifest.py")
    
    @pytest.mark.asyncio
    async def test_auto_register_addon_from_manifest(self):
        """Test automatic addon registration from manifest"""
        mock_db = MagicMock()
        
        with patch('erp.addons.utils.auto_register_addon_from_manifest', return_value=True) as mock_auto:
            result = await auto_register_addon_from_manifest(mock_db, "/path/to/addon")
            assert result is True
            mock_auto.assert_called_once_with(mock_db, "/path/to/addon")


class TestDiscoveryUtilities:
    """Test discovery utility functions"""
    
    def test_discover_addons_in_path(self):
        """Test addon discovery in single path"""
        with patch('erp.addons.utils.discover_addons_in_path', return_value=["addon1", "addon2"]) as mock_discover:
            result = discover_addons_in_path("/path/to/addons")
            assert result == ["addon1", "addon2"]
            mock_discover.assert_called_once_with("/path/to/addons")
    
    def test_discover_addons_in_multiple_paths(self):
        """Test addon discovery in multiple paths"""
        paths = ["/path1", "/path2"]
        expected = {"addon1": "/path1/addon1", "addon2": "/path2/addon2"}
        
        with patch('erp.addons.utils.discover_addons_in_multiple_paths', return_value=expected) as mock_discover:
            result = discover_addons_in_multiple_paths(paths)
            assert result == expected
            mock_discover.assert_called_once_with(paths)
    
    def test_validate_addon_structure(self):
        """Test addon structure validation"""
        with patch('erp.addons.utils.validate_addon_structure', return_value=True) as mock_validate:
            result = validate_addon_structure("/path/to/addon")
            assert result is True
            mock_validate.assert_called_once_with("/path/to/addon")
    
    def test_get_addon_dependencies(self):
        """Test getting addon dependencies"""
        expected_deps = ["base", "web"]
        
        with patch('erp.addons.utils.get_addon_dependencies', return_value=expected_deps) as mock_deps:
            result = get_addon_dependencies("/path/to/addon")
            assert result == expected_deps
            mock_deps.assert_called_once_with("/path/to/addon")
    
    def test_build_dependency_graph(self):
        """Test dependency graph building"""
        addons = {"addon1": ["base"], "addon2": ["addon1"]}
        expected_graph = {"addon1": ["base"], "addon2": ["addon1"], "base": []}
        
        with patch('erp.addons.utils.build_dependency_graph', return_value=expected_graph) as mock_graph:
            result = build_dependency_graph(addons)
            assert result == expected_graph
            mock_graph.assert_called_once_with(addons)
    
    def test_detect_circular_dependencies(self):
        """Test circular dependency detection"""
        dependency_graph = {"addon1": ["addon2"], "addon2": ["addon1"]}
        
        with patch('erp.addons.utils.detect_circular_dependencies', return_value=True) as mock_circular:
            result = detect_circular_dependencies(dependency_graph)
            assert result is True
            mock_circular.assert_called_once_with(dependency_graph)
    
    def test_get_installation_order(self):
        """Test installation order calculation"""
        dependency_graph = {"addon1": ["base"], "addon2": ["addon1"], "base": []}
        expected_order = ["base", "addon1", "addon2"]
        
        with patch('erp.addons.utils.get_installation_order', return_value=expected_order) as mock_order:
            result = get_installation_order(dependency_graph)
            assert result == expected_order
            mock_order.assert_called_once_with(dependency_graph)
    
    def test_find_missing_dependencies(self):
        """Test missing dependency detection"""
        required_deps = ["base", "web", "missing"]
        available_addons = ["base", "web"]
        expected_missing = ["missing"]
        
        with patch('erp.addons.utils.find_missing_dependencies', return_value=expected_missing) as mock_missing:
            result = find_missing_dependencies(required_deps, available_addons)
            assert result == expected_missing
            mock_missing.assert_called_once_with(required_deps, available_addons)


class TestHelperFunctions:
    """Test helper utility functions"""
    
    def test_timing_context(self):
        """Test TimingContext functionality"""
        with patch('erp.addons.utils.TimingContext') as MockTimingContext:
            mock_instance = MockTimingContext.return_value
            mock_instance.__enter__.return_value = mock_instance
            mock_instance.elapsed = 1.5
            
            with TimingContext() as timer:
                pass
            
            MockTimingContext.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_retry_async_operation(self):
        """Test async operation retry"""
        async def test_operation():
            return "success"
        
        with patch('erp.addons.utils.retry_async_operation', return_value="success") as mock_retry:
            result = await retry_async_operation(test_operation, max_retries=3)
            assert result == "success"
            mock_retry.assert_called_once_with(test_operation, max_retries=3)
    
    def test_format_duration(self):
        """Test duration formatting"""
        with patch('erp.addons.utils.format_duration', return_value="1.5s") as mock_format:
            result = format_duration(1.5)
            assert result == "1.5s"
            mock_format.assert_called_once_with(1.5)
    
    def test_safe_get_nested_value(self):
        """Test safe nested value retrieval"""
        data = {"level1": {"level2": {"value": "test"}}}
        
        with patch('erp.addons.utils.safe_get_nested_value', return_value="test") as mock_get:
            result = safe_get_nested_value(data, ["level1", "level2", "value"])
            assert result == "test"
            mock_get.assert_called_once_with(data, ["level1", "level2", "value"])
    
    def test_merge_dictionaries(self):
        """Test dictionary merging"""
        dict1 = {"a": 1, "b": 2}
        dict2 = {"b": 3, "c": 4}
        expected = {"a": 1, "b": 3, "c": 4}
        
        with patch('erp.addons.utils.merge_dictionaries', return_value=expected) as mock_merge:
            result = merge_dictionaries(dict1, dict2)
            assert result == expected
            mock_merge.assert_called_once_with(dict1, dict2)
    
    def test_validate_addon_name(self):
        """Test addon name validation"""
        with patch('erp.addons.utils.validate_addon_name', return_value=True) as mock_validate:
            result = validate_addon_name("valid_addon_name")
            assert result is True
            mock_validate.assert_called_once_with("valid_addon_name")
    
    def test_normalize_addon_name(self):
        """Test addon name normalization"""
        with patch('erp.addons.utils.normalize_addon_name', return_value="normalized_name") as mock_normalize:
            result = normalize_addon_name("Addon Name!")
            assert result == "normalized_name"
            mock_normalize.assert_called_once_with("Addon Name!")
    
    def test_create_error_response(self):
        """Test error response creation"""
        expected = {"success": False, "error": "Test error"}
        
        with patch('erp.addons.utils.create_error_response', return_value=expected) as mock_error:
            result = create_error_response("Test error")
            assert result == expected
            mock_error.assert_called_once_with("Test error")
    
    def test_create_success_response(self):
        """Test success response creation"""
        expected = {"success": True, "data": {"key": "value"}}
        
        with patch('erp.addons.utils.create_success_response', return_value=expected) as mock_success:
            result = create_success_response({"key": "value"})
            assert result == expected
            mock_success.assert_called_once_with({"key": "value"})
    
    def test_chunk_list(self):
        """Test list chunking"""
        data = [1, 2, 3, 4, 5, 6]
        expected = [[1, 2], [3, 4], [5, 6]]
        
        with patch('erp.addons.utils.chunk_list', return_value=expected) as mock_chunk:
            result = chunk_list(data, 2)
            assert result == expected
            mock_chunk.assert_called_once_with(data, 2)
    
    def test_flatten_list(self):
        """Test list flattening"""
        nested = [[1, 2], [3, 4], [5, 6]]
        expected = [1, 2, 3, 4, 5, 6]
        
        with patch('erp.addons.utils.flatten_list', return_value=expected) as mock_flatten:
            result = flatten_list(nested)
            assert result == expected
            mock_flatten.assert_called_once_with(nested)


class TestConstants:
    """Test constant definitions"""
    
    def test_addon_state_constants(self):
        """Test AddonState constants"""
        assert hasattr(AddonState, 'UNINSTALLED')
        assert hasattr(AddonState, 'INSTALLED')
        assert hasattr(AddonState, 'TO_INSTALL')
    
    def test_addon_category_constants(self):
        """Test AddonCategory constants"""
        assert hasattr(AddonCategory, 'BASE')
        assert hasattr(AddonCategory, 'BUSINESS')
    
    def test_manifest_keys_constants(self):
        """Test ManifestKeys constants"""
        assert hasattr(ManifestKeys, 'NAME')
        assert hasattr(ManifestKeys, 'VERSION')
        assert hasattr(ManifestKeys, 'DEPENDS')
    
    def test_all_constants_importable(self):
        """Test that all constants can be imported"""
        from erp.addons.utils import __all__
        
        constant_groups = [
            'AddonState',
            'AddonCategory',
            'ManifestKeys',
            'DefaultValues',
            'FileNames',
            'DatabaseTables',
            'ModuleStates',
            'HookPriorities',
            'ValidationLevels',
            'LogMessages',
            'ErrorCodes'
        ]
        
        for constant_group in constant_groups:
            assert constant_group in __all__
