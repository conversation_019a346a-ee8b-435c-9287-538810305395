"""
Test suite for RouteManager functionality

This module tests:
- Route registration and management
- Route retrieval and removal
- Route statistics and operations
- Comprehensive addon module discovery
"""
import pytest
import sys
import os
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any
from pathlib import Path

from erp.database.memory import RouteManager


class TestRouteManager:
    """Test RouteManager functionality"""
    
    def test_route_manager_initialization(self):
        """Test RouteManager initialization"""
        manager = RouteManager("test_db")
        
        assert manager.db_name == "test_db"
        assert manager.routes == {}
        assert manager._routes_registered is False
    
    @pytest.mark.asyncio
    async def test_register_route(self):
        """Test route registration"""
        manager = RouteManager("test_db")
        
        async def test_handler():
            return "test response"
        
        await manager.register_route("/test", test_handler, methods=["GET"])
        
        routes = await manager.get_routes()
        assert "/test" in routes
        assert routes["/test"]["handler"] == test_handler
        assert routes["/test"]["path"] == "/test"
    
    @pytest.mark.asyncio
    async def test_get_route(self):
        """Test getting specific route"""
        manager = RouteManager("test_db")
        
        async def test_handler():
            return "test"
        
        await manager.register_route("/test", test_handler)
        
        route = await manager.get_route("/test")
        assert route is not None
        assert route["handler"] == test_handler
    
    @pytest.mark.asyncio
    async def test_get_route_nonexistent(self):
        """Test getting nonexistent route"""
        manager = RouteManager("test_db")
        
        route = await manager.get_route("/nonexistent")
        assert route is None
    
    @pytest.mark.asyncio
    async def test_remove_route(self):
        """Test removing route"""
        manager = RouteManager("test_db")
        
        async def test_handler():
            return "test"
        
        await manager.register_route("/test", test_handler)
        
        # Verify route exists
        routes = await manager.get_routes()
        assert "/test" in routes
        
        # Remove route
        result = await manager.remove_route("/test")
        assert result is True
        
        # Verify route is removed
        routes = await manager.get_routes()
        assert "/test" not in routes
    
    @pytest.mark.asyncio
    async def test_remove_nonexistent_route(self):
        """Test removing nonexistent route"""
        manager = RouteManager("test_db")
        
        result = await manager.remove_route("/nonexistent")
        assert result is False
    
    def test_get_route_stats(self):
        """Test getting route statistics"""
        manager = RouteManager("test_db")
        
        # Add some test routes
        manager.routes = {
            "/route1": {"handler": lambda: None},
            "/route2": {"handler": lambda: None}
        }
        
        stats = manager.get_route_stats()
        
        assert 'routes_count' in stats
        assert 'routes_registered' in stats
        assert stats['routes_count'] == 2
        assert isinstance(stats['routes_registered'], bool)
    
    @pytest.mark.asyncio
    async def test_register_multiple_routes(self):
        """Test registering multiple routes"""
        manager = RouteManager("test_db")
        
        async def handler1():
            return "response1"
        
        async def handler2():
            return "response2"
        
        await manager.register_route("/route1", handler1, methods=["GET"])
        await manager.register_route("/route2", handler2, methods=["POST"])
        
        routes = await manager.get_routes()
        assert len(routes) == 2
        assert "/route1" in routes
        assert "/route2" in routes
        assert routes["/route1"]["handler"] == handler1
        assert routes["/route2"]["handler"] == handler2
    
    @pytest.mark.asyncio
    async def test_route_overwrite(self):
        """Test overwriting existing route"""
        manager = RouteManager("test_db")
        
        async def handler1():
            return "response1"
        
        async def handler2():
            return "response2"
        
        # Register initial route
        await manager.register_route("/test", handler1)
        
        # Overwrite with new handler
        await manager.register_route("/test", handler2)
        
        route = await manager.get_route("/test")
        assert route["handler"] == handler2
    
    @pytest.mark.asyncio
    async def test_route_methods_handling(self):
        """Test route registration with different HTTP methods"""
        manager = RouteManager("test_db")
        
        async def get_handler():
            return "GET response"
        
        async def post_handler():
            return "POST response"
        
        await manager.register_route("/api/test", get_handler, methods=["GET"])
        await manager.register_route("/api/create", post_handler, methods=["POST"])
        
        routes = await manager.get_routes()
        assert "/api/test" in routes
        assert "/api/create" in routes
    
    def test_route_stats_empty(self):
        """Test route statistics when no routes are registered"""
        manager = RouteManager("test_db")

        stats = manager.get_route_stats()

        assert stats['routes_count'] == 0
        assert stats['routes_registered'] is False


class TestRouteDiscovery:
    """Test comprehensive route discovery functionality"""

    @pytest.mark.asyncio
    async def test_discover_all_addon_modules(self):
        """Test discovery of routes from all addon modules"""
        manager = RouteManager("test_db")

        # Mock a module with route handlers
        mock_module = MagicMock()
        mock_handler = MagicMock()
        mock_handler._route_metadata = {
            'path': '/test/route',
            'methods': ['GET'],
            'type': 'http'
        }
        mock_module._route_handlers = [mock_handler]

        # Add mock module to sys.modules
        test_module_name = 'erp.addons.test_addon.test_module'
        sys.modules[test_module_name] = mock_module

        try:
            # Test discovery
            route_count = await manager._discover_all_addon_modules('test_addon')

            # Should find the route in the mock module
            assert route_count >= 1

        finally:
            # Clean up
            if test_module_name in sys.modules:
                del sys.modules[test_module_name]

    @pytest.mark.asyncio
    async def test_discover_filesystem_modules(self):
        """Test discovery of modules from filesystem"""
        manager = RouteManager("test_db")

        # Mock addon import manager
        mock_addon_paths = {'test_addon': '/fake/path/to/addon'}

        # Mock filesystem structure
        mock_py_files = [
            Path('/fake/path/to/addon/controllers/test_controller.py'),
            Path('/fake/path/to/addon/models/test_model.py'),
            Path('/fake/path/to/addon/utils/test_utils.py')
        ]

        # Ensure modules are not already loaded
        module_names = [
            'erp.addons.test_addon.controllers.test_controller',
            'erp.addons.test_addon.models.test_model',
            'erp.addons.test_addon.utils.test_utils'
        ]

        # Clean up any existing modules first
        for module_name in module_names:
            if module_name in sys.modules:
                del sys.modules[module_name]

        with patch('erp.addons._addon_import_manager.list_registered_addons', return_value=mock_addon_paths), \
             patch('pathlib.Path.exists', return_value=True), \
             patch('pathlib.Path.rglob', return_value=mock_py_files), \
             patch('importlib.import_module') as mock_import:

            # Mock successful import - modules will be added to sys.modules by the import
            def mock_import_side_effect(module_name):
                mock_module = MagicMock()
                mock_module._route_handlers = []
                sys.modules[module_name] = mock_module
                return mock_module

            mock_import.side_effect = mock_import_side_effect

            try:
                route_count = await manager._discover_filesystem_modules('test_addon')

                # Should attempt to import modules (3 Python files)
                assert mock_import.call_count == 3

            finally:
                # Clean up
                for module_name in module_names:
                    if module_name in sys.modules:
                        del sys.modules[module_name]

    @pytest.mark.asyncio
    async def test_discover_addon_routes_for_addon(self):
        """Test complete addon route discovery"""
        manager = RouteManager("test_db")

        # Mock main addon module
        mock_main_module = MagicMock()
        mock_main_module._route_handlers = []
        sys.modules['erp.addons.test_addon'] = mock_main_module

        # Mock other addon modules
        mock_controller_module = MagicMock()
        mock_handler = MagicMock()
        mock_handler._route_metadata = {
            'path': '/addon/test',
            'methods': ['GET'],
            'type': 'http'
        }
        mock_controller_module._route_handlers = [mock_handler]
        sys.modules['erp.addons.test_addon.controllers.main'] = mock_controller_module

        try:
            route_count = await manager._discover_addon_routes_for_addon('test_addon')

            # Should discover routes from addon modules
            assert route_count >= 1

        finally:
            # Clean up
            for module_name in ['erp.addons.test_addon', 'erp.addons.test_addon.controllers.main']:
                if module_name in sys.modules:
                    del sys.modules[module_name]

    def test_is_addon_module(self):
        """Test addon module identification"""
        manager = RouteManager("test_db")

        # Test valid addon modules
        assert manager._is_addon_module('erp.addons.test_addon') is True
        assert manager._is_addon_module('erp.addons.test_addon.controllers') is True
        assert manager._is_addon_module('erp.addons.test_addon.models.test_model') is True

        # Test invalid modules
        assert manager._is_addon_module('erp.addons') is False
        assert manager._is_addon_module('erp.models') is False
        assert manager._is_addon_module('addons.test_addon') is False  # No backward compatibility
        assert manager._is_addon_module('some.other.module') is False

    def test_extract_routes_from_module(self):
        """Test route extraction from module"""
        manager = RouteManager("test_db")

        # Mock module with route handlers
        mock_module = MagicMock()
        mock_handler1 = MagicMock()
        mock_handler1._route_metadata = {
            'path': '/test/route1',
            'methods': ['GET'],
            'type': 'http'
        }
        mock_handler2 = MagicMock()
        mock_handler2._route_metadata = {
            'path': '/test/route2',
            'methods': ['POST'],
            'type': 'json'
        }
        mock_module._route_handlers = [mock_handler1, mock_handler2]

        route_count = manager._extract_routes_from_module(mock_module)

        assert route_count == 2
        assert '/test/route1' in manager.routes
        assert '/test/route2' in manager.routes
