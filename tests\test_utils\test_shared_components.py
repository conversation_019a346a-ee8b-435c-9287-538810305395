"""
Test suite for shared utility components
Tests the new shared RegistryUpdater and ValidationManager utilities.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from erp.utils.registry import RegistryUpdater, get_registry_updater
from erp.utils.validation import Val<PERSON><PERSON><PERSON><PERSON><PERSON>, get_validation_manager


class TestRegistryUpdater:
    """Test RegistryUpdater functionality"""
    
    def test_registry_updater_initialization(self):
        """Test RegistryUpdater initialization"""
        updater = RegistryUpdater()
        
        assert hasattr(updater, 'logger')
    
    def test_get_registry_updater_singleton(self):
        """Test that get_registry_updater returns singleton instance"""
        updater1 = get_registry_updater()
        updater2 = get_registry_updater()
        
        assert updater1 is updater2
        assert isinstance(updater1, RegistryUpdater)
    
    @pytest.mark.asyncio
    async def test_update_registry_after_module_action_success(self):
        """Test successful registry update after module action"""
        updater = RegistryUpdater()
        
        # Mock environment
        mock_env = MagicMock()
        mock_env.cr.db_name = 'test_db'
        
        with patch('erp.database.memory.registry_manager.MemoryRegistryManager') as mock_manager:
            mock_manager.update_registry_after_module_action.return_value = True
            
            result = await updater.update_registry_after_module_action(
                mock_env, 'test_module', 'install'
            )
            
            assert result is True
            mock_manager.update_registry_after_module_action.assert_called_once_with(
                'test_db', 'test_module', 'install'
            )
    
    @pytest.mark.asyncio
    async def test_update_registry_after_module_action_failure(self):
        """Test registry update failure"""
        updater = RegistryUpdater()
        
        mock_env = MagicMock()
        mock_env.cr.db_name = 'test_db'
        
        with patch('erp.database.memory.registry_manager.MemoryRegistryManager') as mock_manager:
            mock_manager.update_registry_after_module_action.return_value = False
            
            result = await updater.update_registry_after_module_action(
                mock_env, 'test_module', 'install'
            )
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_validate_module_in_registry_success(self):
        """Test successful module validation in registry"""
        updater = RegistryUpdater()
        
        mock_env = MagicMock()
        mock_env.cr.db_name = 'test_db'
        
        mock_registry = MagicMock()
        mock_registry.installed_modules = {'test_module'}
        mock_registry.model_metadata_manager.has_model.return_value = True
        
        with patch('erp.database.memory.registry_manager.MemoryRegistryManager') as mock_manager:
            mock_manager.get_registry.return_value = mock_registry
            
            result = await updater.validate_module_in_registry(
                mock_env, 'test_module', ['test.model']
            )
            
            assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_module_in_registry_module_not_found(self):
        """Test module validation when module not in registry"""
        updater = RegistryUpdater()
        
        mock_env = MagicMock()
        mock_env.cr.db_name = 'test_db'
        
        mock_registry = MagicMock()
        mock_registry.installed_modules = set()  # Module not installed
        
        with patch('erp.database.memory.registry_manager.MemoryRegistryManager') as mock_manager:
            mock_manager.get_registry.return_value = mock_registry
            
            result = await updater.validate_module_in_registry(mock_env, 'test_module')
            
            assert result is False


class TestValidationManager:
    """Test ValidationManager functionality"""
    
    def test_validation_manager_initialization(self):
        """Test ValidationManager initialization"""
        manager = ValidationManager()
        
        assert hasattr(manager, 'logger')
    
    def test_get_validation_manager_singleton(self):
        """Test that get_validation_manager returns singleton instance"""
        manager1 = get_validation_manager()
        manager2 = get_validation_manager()
        
        assert manager1 is manager2
        assert isinstance(manager1, ValidationManager)
    
    @pytest.mark.asyncio
    async def test_validate_tables_exist_success(self):
        """Test successful table existence validation"""
        manager = ValidationManager()
        
        mock_db = AsyncMock()
        mock_db.fetchval.return_value = True  # Table exists
        
        result = await manager.validate_tables_exist(mock_db, ['test_table'])
        
        assert result is True
        mock_db.fetchval.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_validate_tables_exist_missing_table(self):
        """Test table validation when table is missing"""
        manager = ValidationManager()
        
        mock_db = AsyncMock()
        mock_db.fetchval.return_value = False  # Table doesn't exist
        
        result = await manager.validate_tables_exist(mock_db, ['missing_table'])
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_validate_module_registration_success(self):
        """Test successful module registration validation"""
        manager = ValidationManager()
        
        mock_db = AsyncMock()
        mock_db.fetchrow.return_value = {
            'name': 'test_module',
            'state': 'installed',
            'installable': True
        }
        
        result = await manager.validate_module_registration(mock_db, 'test_module')
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_module_registration_wrong_state(self):
        """Test module registration validation with wrong state"""
        manager = ValidationManager()
        
        mock_db = AsyncMock()
        mock_db.fetchrow.return_value = {
            'name': 'test_module',
            'state': 'uninstalled',  # Wrong state
            'installable': True
        }
        
        result = await manager.validate_module_registration(mock_db, 'test_module')
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_get_validation_report(self):
        """Test comprehensive validation report generation"""
        manager = ValidationManager()
        
        mock_db = AsyncMock()
        mock_env = MagicMock()
        
        # Mock all validation methods to return True
        with patch.object(manager, 'validate_tables_exist', return_value=True):
            with patch.object(manager, 'validate_models_in_ir_metadata', return_value=True):
                with patch.object(manager, 'validate_module_registration', return_value=True):
                    with patch.object(manager, 'validate_module_in_registry', return_value=True):
                        with patch.object(manager, 'validate_constraints_exist', return_value=True):
                            with patch.object(manager, 'validate_indexes_exist', return_value=True):
                                
                                report = await manager.get_validation_report(
                                    mock_db, mock_env, 'test_module',
                                    ['test_table'], ['test.model'],
                                    ['test_constraint'], ['test_index']
                                )
                                
                                assert report['module_name'] == 'test_module'
                                assert report['overall_valid'] is True
                                assert report['schema_valid'] is True
                                assert report['metadata_valid'] is True
                                assert report['registration_valid'] is True
                                assert report['registry_valid'] is True
                                assert report['constraints_valid'] is True
                                assert report['indexes_valid'] is True
