"""
Test suite for database filter processor functionality

This module tests:
- Database name filtering and pattern matching
- Regex pattern validation
- Filter fallback behavior
- Error handling for invalid patterns
"""
import pytest

from erp.database import DatabaseFilterProcessor


class TestDatabaseFilterProcessor:
    """Test DatabaseFilterProcessor functionality"""
    
    def test_database_filter_no_filter(self):
        """Test database filtering with no filter set"""
        result = DatabaseFilterProcessor.check_database_matches_filter("any_db", None)
        assert result is True
    
    def test_database_filter_matching_pattern(self):
        """Test database filtering with matching pattern"""
        result = DatabaseFilterProcessor.check_database_matches_filter("test_db", r"^test_.*")
        assert result is True
    
    def test_database_filter_non_matching_pattern(self):
        """Test database filtering with non-matching pattern"""
        result = DatabaseFilterProcessor.check_database_matches_filter("prod_db", r"^test_.*")
        assert result is False
    
    def test_database_filter_invalid_pattern(self):
        """Test database filtering with invalid regex pattern"""
        # Should return True for invalid patterns (fallback behavior)
        result = DatabaseFilterProcessor.check_database_matches_filter("any_db", "[invalid")
        assert result is True
