"""
Model metadata management for database memory registry
"""
import time
from typing import Dict, Optional, Any
from ...logging import get_logger


class ModelMetadataManager:
    """Manages model and field metadata for database registry"""

    def __init__(self, db_name: str):
        self.db_name = db_name
        self.models: Dict[str, Dict[str, Any]] = {}  # Model name -> model metadata
        self.model_fields: Dict[str, Dict[str, Dict[str, Any]]] = {}  # Model name -> field name -> field metadata
        self._logger = get_logger(f"{__name__}.{db_name}")

    async def load_models_metadata(self, db_manager) -> None:
        """Load model metadata from ir_model table"""
        try:
            models_query = """
                SELECT model, name, info, description, state, table_name_db
                FROM ir_model
                ORDER BY model
            """

            models = await db_manager.fetch(models_query)
            if not models:
                self._logger.warning("No models found in ir_model table")
                return

            loaded_count = 0
            for model_data in models:
                try:
                    model_name = model_data['model']
                    if not model_name:
                        self._logger.warning("Skipping model with empty name")
                        continue

                    self.models[model_name] = {
                        'name': model_data.get('name', model_name),
                        'model': model_name,
                        'info': model_data.get('info', ''),
                        'description': model_data.get('description', ''),
                        'state': model_data.get('state', 'manual'),
                        'table': model_data.get('table_name_db', ''),
                        'loaded_at': time.time()
                    }
                    loaded_count += 1
                except Exception as e:
                    self._logger.warning(f"Failed to load model {model_data.get('model', 'unknown')}: {e}")
                    continue

            self._logger.info(f"Loaded {loaded_count} model definitions")

        except Exception as e:
            self._logger.error(f"Failed to load models metadata: {e}")
            raise

    async def load_fields_metadata(self, db_manager) -> None:
        """Load field metadata from ir_model_fields table"""
        try:
            fields_query = """
                SELECT model, name, field_description, help, ttype, required, readonly,
                       store, is_indexed, translate, size, digits, domain, context
                FROM ir_model_fields
                ORDER BY model, name
            """

            fields = await db_manager.fetch(fields_query)
            if not fields:
                self._logger.warning("No fields found in ir_model_fields table")
                return

            loaded_count = 0
            for field_data in fields:
                try:
                    model_name = field_data['model']
                    field_name = field_data['name']

                    if not model_name or not field_name:
                        self._logger.warning("Skipping field with empty model or field name")
                        continue

                    if model_name not in self.model_fields:
                        self.model_fields[model_name] = {}

                    self.model_fields[model_name][field_name] = {
                        'name': field_name,
                        'field_description': field_data.get('field_description', ''),
                        'help': field_data.get('help', ''),
                        'ttype': field_data.get('ttype', 'char'),
                        'required': field_data.get('required', False),
                        'readonly': field_data.get('readonly', False),
                        'store': field_data.get('store', True),
                        'index': field_data.get('index', False),
                        'translate': field_data.get('translate', False),
                        'size': field_data.get('size'),
                        'digits': field_data.get('digits'),
                        'domain': field_data.get('domain', '[]'),
                        'context': field_data.get('context', '{}'),
                        'loaded_at': time.time()
                    }
                    loaded_count += 1
                except Exception as e:
                    self._logger.warning(f"Failed to load field {field_data.get('model', 'unknown')}.{field_data.get('name', 'unknown')}: {e}")
                    continue

            self._logger.info(f"Loaded {loaded_count} field definitions")

        except Exception as e:
            self._logger.error(f"Failed to load fields metadata: {e}")
            raise

    async def get_model_metadata(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific model"""
        return self.models.get(model_name)

    async def get_all_models_metadata(self) -> Dict[str, Dict[str, Any]]:
        """Get metadata for all models"""
        return self.models.copy()

    async def get_model_fields_metadata(self, model_name: str) -> Optional[Dict[str, Dict[str, Any]]]:
        """Get field metadata for a specific model"""
        return self.model_fields.get(model_name)

    async def get_field_metadata(self, model_name: str, field_name: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific field of a model"""
        model_fields = self.model_fields.get(model_name)
        if model_fields:
            return model_fields.get(field_name)
        return None

    async def get_all_fields_metadata(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """Get metadata for all fields of all models"""
        return self.model_fields.copy()

    async def get_model_data(self, model_name: str) -> Optional[Dict[str, Any]]:
        """
        Get combined model data with fields

        Args:
            model_name: Name of the model

        Returns:
            Model data dictionary or None if not found
        """
        # Get model metadata
        model_metadata = await self.get_model_metadata(model_name)
        if not model_metadata:
            return None

        # Get field metadata
        fields_metadata = await self.get_model_fields_metadata(model_name)

        # Combine into model data
        model_data = {
            **model_metadata,
            'fields': fields_metadata or {}
        }

        return model_data

    async def set_model_data(self, model_name: str, model_data: Dict[str, Any]) -> None:
        """
        Set model data and metadata

        Args:
            model_name: Name of the model
            model_data: Model data dictionary
        """
        # Store model metadata
        self.models[model_name] = {
            'name': model_data.get('name', model_name),
            'model': model_name,
            'info': model_data.get('info', ''),
            'description': model_data.get('description', ''),
            'state': model_data.get('state', 'manual'),
            'table': model_data.get('table_name_db', ''),
            'loaded_at': time.time()
        }

        # Store field metadata if provided
        fields = model_data.get('fields', {})
        if fields:
            if isinstance(fields, list):
                # Convert list of field names to dict
                field_dict = {}
                for field_name in fields:
                    field_dict[field_name] = {
                        'name': field_name,
                        'field_description': field_name,
                        'ttype': 'char',
                        'loaded_at': time.time()
                    }
                self.model_fields[model_name] = field_dict
            elif isinstance(fields, dict):
                # Store field dict directly
                self.model_fields[model_name] = fields

        self._logger.debug(f"Set model data for: {model_name}")

    def clear_metadata(self) -> None:
        """Clear all model and field metadata"""
        self.models.clear()
        self.model_fields.clear()
        self._logger.debug("Model metadata cleared")

    def get_metadata_stats(self) -> Dict[str, Any]:
        """Get metadata statistics"""
        total_fields = sum(len(fields) for fields in self.model_fields.values())
        return {
            'models_count': len(self.models),
            'total_fields_count': total_fields,
            'models_with_fields': len(self.model_fields)
        }
