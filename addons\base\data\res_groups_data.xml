<?xml version="1.0" encoding="utf-8"?>
<data noupdate="1">
    
    <!-- Module Categories -->
    <record id="module_category_administration" model="ir.module.category">
        <field name="name">Administration</field>
        <field name="description">User access to administration features</field>
        <field name="sequence">1</field>
        <field name="visible">True</field>
    </record>
    
    <record id="module_category_base" model="ir.module.category">
        <field name="name">Base</field>
        <field name="description">Base system functionality</field>
        <field name="sequence">2</field>
        <field name="visible">True</field>
    </record>
    
    <record id="module_category_user_types" model="ir.module.category">
        <field name="name">User Types</field>
        <field name="description">Different types of users</field>
        <field name="sequence">3</field>
        <field name="visible">True</field>
        <field name="exclusive">True</field>
    </record>
    
    <!-- Base Groups -->
    <record id="group_user" model="res.groups">
        <field name="name">User</field>
        <field name="category_id" ref="module_category_user_types"/>
        <field name="comment">Basic user access</field>
        <field name="sequence">10</field>
    </record>
    
    <record id="group_system" model="res.groups">
        <field name="name">System</field>
        <field name="category_id" ref="module_category_administration"/>
        <field name="comment">System administration access</field>
        <field name="sequence">1</field>
        <field name="implied_ids" eval="[(4, ref('group_user'))]"/>
    </record>
    

    <record id="group_user_admin" model="res.groups">
        <field name="name">User Administrator</field>
        <field name="category_id" ref="module_category_administration"/>
        <field name="comment">Can manage users and their access rights</field>
        <field name="sequence">5</field>
        <field name="implied_ids" eval="[(4, ref('group_system'))]"/>
    </record>
    
    <!-- Portal/External User Groups -->
    <record id="group_portal" model="res.groups">
        <field name="name">Portal</field>
        <field name="category_id" ref="module_category_user_types"/>
        <field name="comment">Portal users with limited access</field>
        <field name="sequence">20</field>
        <field name="share">True</field>
    </record>
    
    <record id="group_public" model="res.groups">
        <field name="name">Public</field>
        <field name="category_id" ref="module_category_user_types"/>
        <field name="comment">Public users (anonymous access)</field>
        <field name="sequence">30</field>
        <field name="share">True</field>
    </record>
    
</data>
