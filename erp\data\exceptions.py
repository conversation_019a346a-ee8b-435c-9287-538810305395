"""
Exceptions for data loading system
"""


class DataLoadingError(Exception):
    """Base exception for data loading errors"""
    pass


class XMLParsingError(DataLoadingError):
    """Exception raised when XML parsing fails"""
    pass


class ModelNotFoundError(DataLoadingError):
    """Exception raised when referenced model is not found"""
    pass


class RecordCreationError(DataLoadingError):
    """Exception raised when record creation fails"""
    pass


class XMLIDError(DataLoadingError):
    """Exception raised when XML ID operations fail"""
    pass
