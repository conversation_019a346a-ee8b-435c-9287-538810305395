"""
Logging Factories Module
Factory classes for creating modern logging components based on configuration
"""
import logging
import logging.handlers
import sys
import json
from typing import Dict, Any, Optional, Type
from abc import ABC, abstractmethod
from datetime import datetime

from ..config import FormatterConfig, HandlerConfig, FilterConfig, LoggerConfig
from ..adapters import (
    LoggingAdapter, ConsoleAdapter, FileAdapter, DatabaseAdapter, RemoteAdapter
)


class ComponentFactory(ABC):
    """Abstract base class for component factories"""
    
    @abstractmethod
    def create(self, name: str, config: Any) -> Any:
        """Create a component based on configuration"""
        pass
    
    @abstractmethod
    def get_supported_types(self) -> Dict[str, Type]:
        """Get mapping of type names to classes"""
        pass


class ModernFormatter(logging.Formatter):
    """Modern ERP formatter with enhanced features"""

    def __init__(self, format_type: str = 'standard', use_colors: bool = False, use_unicode: bool = True):
        self.format_type = format_type
        self.use_colors = use_colors
        self.use_unicode = use_unicode

        # Choose format based on type
        if format_type == 'json':
            # JSON formatter doesn't use traditional format string
            super().__init__()
        elif format_type == 'colored' and use_colors:
            separator = " │ " if use_unicode else " | "
            fmt = f"%(asctime)s{separator}%(levelname)-8s{separator}%(name)-25s{separator}%(message)s"
            super().__init__(fmt, datefmt="%H:%M:%S")
        else:
            # Standard ERP format
            separator = " │ " if use_unicode else " | "
            fmt = f"%(asctime)s{separator}%(levelname)-8s{separator}%(name)-25s{separator}%(message)s"
            super().__init__(fmt, datefmt="%Y-%m-%d %H:%M:%S")

    def format(self, record):
        """Format log record with modern enhancements"""
        if self.format_type == 'json':
            return self._format_json(record)
        elif self.format_type == 'colored' and self.use_colors:
            return self._format_colored(record)
        else:
            return self._format_standard(record)

    def _format_json(self, record):
        """Format as JSON"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': getattr(record, 'module', ''),
            'function': getattr(record, 'funcName', ''),
            'line': getattr(record, 'lineno', 0),
        }

        # Add ERP-specific fields
        erp_fields = ['user_id', 'database', 'request_id', 'addon_name', 'model_name']
        for field in erp_fields:
            if hasattr(record, field):
                log_entry[field] = getattr(record, field)

        return json.dumps(log_entry, default=str)

    def _format_colored(self, record):
        """Format with colors"""
        # Color codes
        colors = {
            'DEBUG': '\033[96m',    # Bright cyan
            'INFO': '\033[92m',     # Bright green
            'WARNING': '\033[93m',  # Bright yellow
            'ERROR': '\033[91m',    # Bright red
            'CRITICAL': '\033[97m\033[41m',  # White on red
            'RESET': '\033[0m'
        }

        # Store original values
        original_levelname = record.levelname
        original_name = record.name

        try:
            # Apply colors
            color = colors.get(record.levelname, '')
            reset = colors['RESET']

            record.levelname = f"{color}{record.levelname:<8s}{reset}"

            # Truncate module name for consistency
            module_name = record.name
            if len(module_name) > 25:
                module_name = "..." + module_name[-22:]
            record.name = f"{module_name:<25s}"

            # Format the message
            formatted = super().format(record)

            return formatted

        finally:
            # Restore original values
            record.levelname = original_levelname
            record.name = original_name

    def _format_standard(self, record):
        """Format with standard ERP formatting"""
        # Store original values
        original_levelname = record.levelname
        original_name = record.name

        try:
            # Add ERP-specific attributes with defaults
            if not hasattr(record, 'user_id'):
                record.user_id = 'system'
            if not hasattr(record, 'database'):
                record.database = 'default'

            # Ensure consistent padding
            record.levelname = f"{record.levelname:<8s}"

            # Truncate and pad module name
            module_name = record.name
            if len(module_name) > 25:
                module_name = "..." + module_name[-22:]
            record.name = f"{module_name:<25s}"

            return super().format(record)

        finally:
            # Restore original values
            record.levelname = original_levelname
            record.name = original_name


class FormatterFactory(ComponentFactory):
    """Factory for creating modern formatters"""

    def create(self, name: str, config: FormatterConfig) -> logging.Formatter:
        """Create a formatter based on configuration"""
        if config.type == 'json':
            return ModernFormatter(format_type='json')
        elif config.type == 'colored':
            use_colors = getattr(config, 'use_colors', True)
            use_unicode = getattr(config, 'use_unicode', True)
            return ModernFormatter(format_type='colored', use_colors=use_colors, use_unicode=use_unicode)
        else:
            # Default to standard ERP format
            use_unicode = getattr(config, 'use_unicode', True)
            return ModernFormatter(format_type='standard', use_unicode=use_unicode)

    def get_supported_types(self) -> Dict[str, Type]:
        """Get mapping of formatter type names to classes"""
        return {
            'standard': ModernFormatter,
            'json': ModernFormatter,
            'colored': ModernFormatter,
        }


class ModernFilter(logging.Filter):
    """Modern logging filter with enhanced features"""

    def __init__(self, filter_type: str = 'level', **kwargs):
        super().__init__()
        self.filter_type = filter_type
        self.config = kwargs

    def filter(self, record):
        """Filter log records based on type"""
        if self.filter_type == 'level':
            return self._filter_level(record)
        elif self.filter_type == 'module':
            return self._filter_module(record)
        elif self.filter_type == 'performance':
            return self._filter_performance(record)
        else:
            return True

    def _filter_level(self, record):
        """Filter by log level"""
        min_level = self.config.get('min_level')
        max_level = self.config.get('max_level')

        if min_level and record.levelno < getattr(logging, min_level.upper()):
            return False
        if max_level and record.levelno > getattr(logging, max_level.upper()):
            return False

        return True

    def _filter_module(self, record):
        """Filter by module patterns"""
        import re

        include_patterns = self.config.get('include_patterns', [])
        exclude_patterns = self.config.get('exclude_patterns', [])

        # Check exclude patterns first
        for pattern in exclude_patterns:
            if re.search(pattern, record.name):
                return False

        # If include patterns exist, record must match at least one
        if include_patterns:
            for pattern in include_patterns:
                if re.search(pattern, record.name):
                    return True
            return False

        return True

    def _filter_performance(self, record):
        """Filter performance-related logs"""
        duration = getattr(record, 'duration', None)
        if duration is None:
            return True

        min_duration = self.config.get('min_duration')
        max_duration = self.config.get('max_duration')

        if min_duration is not None and duration < min_duration:
            return False
        if max_duration is not None and duration > max_duration:
            return False

        return True


class FilterFactory(ComponentFactory):
    """Factory for creating modern filters"""

    def create(self, name: str, config: FilterConfig) -> logging.Filter:
        """Create a filter based on configuration"""
        kwargs = {}

        if config.type == 'level':
            if hasattr(config, 'min_level') and config.min_level:
                kwargs['min_level'] = config.min_level
            if hasattr(config, 'max_level') and config.max_level:
                kwargs['max_level'] = config.max_level

        elif config.type == 'module':
            if hasattr(config, 'include_patterns') and config.include_patterns:
                kwargs['include_patterns'] = config.include_patterns
            if hasattr(config, 'exclude_patterns') and config.exclude_patterns:
                kwargs['exclude_patterns'] = config.exclude_patterns

        elif config.type == 'performance':
            if hasattr(config, 'min_duration') and config.min_duration is not None:
                kwargs['min_duration'] = config.min_duration
            if hasattr(config, 'max_duration') and config.max_duration is not None:
                kwargs['max_duration'] = config.max_duration

        return ModernFilter(filter_type=config.type, **kwargs)

    def get_supported_types(self) -> Dict[str, Type]:
        """Get mapping of filter type names to classes"""
        return {
            'level': ModernFilter,
            'module': ModernFilter,
            'performance': ModernFilter,
        }


class HandlerFactory(ComponentFactory):
    """Factory for creating modern handlers"""

    def __init__(self, formatter_factory: FormatterFactory, filter_factory: FilterFactory):
        self.formatter_factory = formatter_factory
        self.filter_factory = filter_factory

    def create(self, name: str, config: HandlerConfig,
               formatters: Dict[str, logging.Formatter],
               filters: Dict[str, logging.Filter]) -> logging.Handler:
        """Create a handler based on configuration"""

        # Create handler based on type
        if config.type == 'console':
            stream = sys.stderr if getattr(config, 'stream', 'stdout') == 'stderr' else sys.stdout
            handler = logging.StreamHandler(stream)

        elif config.type == 'file':
            if not getattr(config, 'filename', None):
                raise ValueError(f"Handler '{name}' of type 'file' requires filename")
            handler = logging.FileHandler(config.filename, encoding='utf-8')

        elif config.type == 'rotating_file':
            if not getattr(config, 'filename', None):
                raise ValueError(f"Handler '{name}' of type 'rotating_file' requires filename")
            max_bytes = getattr(config, 'max_bytes', 10*1024*1024)  # 10MB default
            backup_count = getattr(config, 'backup_count', 5)
            handler = logging.handlers.RotatingFileHandler(
                config.filename, maxBytes=max_bytes, backupCount=backup_count, encoding='utf-8'
            )

        elif config.type == 'timed_rotating_file':
            if not getattr(config, 'filename', None):
                raise ValueError(f"Handler '{name}' of type 'timed_rotating_file' requires filename")
            when = getattr(config, 'when', 'midnight')
            interval = getattr(config, 'interval', 1)
            backup_count = getattr(config, 'backup_count', 7)
            handler = logging.handlers.TimedRotatingFileHandler(
                config.filename, when=when, interval=interval, backupCount=backup_count, encoding='utf-8'
            )

        elif config.type == 'database':
            # For database handlers, we'll use a custom handler that works with adapters
            handler = DatabaseLogHandler(
                table_name=getattr(config, 'table_name', 'system_logs'),
                max_buffer_size=getattr(config, 'max_buffer_size', 100),
                flush_interval=getattr(config, 'flush_interval', 30.0)
            )

        else:
            raise ValueError(f"Unknown handler type: {config.type}")

        # Set level
        level = getattr(config, 'level', 'INFO')
        handler.setLevel(getattr(logging, level.upper()))

        # Set formatter
        formatter_name = getattr(config, 'formatter', 'default')
        if formatter_name in formatters:
            handler.setFormatter(formatters[formatter_name])
        else:
            # Create a default formatter if none specified
            default_formatter = ModernFormatter()
            handler.setFormatter(default_formatter)

        # Add filters
        filter_names = getattr(config, 'filters', [])
        for filter_name in filter_names:
            if filter_name in filters:
                handler.addFilter(filters[filter_name])

        return handler

    def get_supported_types(self) -> Dict[str, Type]:
        """Get mapping of handler type names to classes"""
        return {
            'console': logging.StreamHandler,
            'file': logging.FileHandler,
            'rotating_file': logging.handlers.RotatingFileHandler,
            'timed_rotating_file': logging.handlers.TimedRotatingFileHandler,
            'database': 'DatabaseLogHandler',
        }


class DatabaseLogHandler(logging.Handler):
    """Custom database log handler that works with modern adapters"""

    def __init__(self, table_name: str = 'system_logs', max_buffer_size: int = 100, flush_interval: float = 30.0):
        super().__init__()
        self.table_name = table_name
        self.adapter = None
        # Initialize database adapter when needed

    def emit(self, record):
        """Emit a log record to database"""
        try:
            if not self.adapter:
                # Initialize database adapter on first use
                self.adapter = DatabaseAdapter(table_name=self.table_name)

            self.adapter.emit(record)
        except Exception:
            # Don't let database errors break logging
            pass

    def flush(self):
        """Flush any buffered logs"""
        if self.adapter:
            self.adapter.flush()

    def close(self):
        """Close the handler"""
        if self.adapter:
            self.adapter.close()
        super().close()


class LoggerFactory(ComponentFactory):
    """Factory for creating loggers"""

    def create(self, name: str, config: LoggerConfig,
               handlers: Dict[str, logging.Handler],
               filters: Dict[str, logging.Filter]) -> logging.Logger:
        """Create a logger based on configuration"""
        logger = logging.getLogger(name)

        # Set level
        level = getattr(config, 'level', 'INFO')
        logger.setLevel(getattr(logging, level.upper()))

        # Clear existing handlers
        logger.handlers.clear()

        # Add handlers
        handler_names = getattr(config, 'handlers', [])
        for handler_name in handler_names:
            if handler_name in handlers:
                logger.addHandler(handlers[handler_name])

        # Add filters
        filter_names = getattr(config, 'filters', [])
        for filter_name in filter_names:
            if filter_name in filters:
                logger.addFilter(filters[filter_name])

        # Set propagation
        propagate = getattr(config, 'propagate', True)
        logger.propagate = propagate

        return logger

    def get_supported_types(self) -> Dict[str, Type]:
        """Get mapping of logger type names to classes"""
        return {'logger': logging.Logger}


class ComponentFactoryRegistry:
    """Registry for component factories"""
    
    def __init__(self):
        self.formatter_factory = FormatterFactory()
        self.filter_factory = FilterFactory()
        self.handler_factory = HandlerFactory(self.formatter_factory, self.filter_factory)
        self.logger_factory = LoggerFactory()
    
    def get_formatter_factory(self) -> FormatterFactory:
        """Get formatter factory"""
        return self.formatter_factory
    
    def get_filter_factory(self) -> FilterFactory:
        """Get filter factory"""
        return self.filter_factory
    
    def get_handler_factory(self) -> HandlerFactory:
        """Get handler factory"""
        return self.handler_factory
    
    def get_logger_factory(self) -> LoggerFactory:
        """Get logger factory"""
        return self.logger_factory
    
    def get_all_supported_types(self) -> Dict[str, Dict[str, Type]]:
        """Get all supported types from all factories"""
        return {
            'formatters': self.formatter_factory.get_supported_types(),
            'filters': self.filter_factory.get_supported_types(),
            'handlers': self.handler_factory.get_supported_types(),
            'loggers': self.logger_factory.get_supported_types(),
        }


# Global factory registry instance
_factory_registry: Optional[ComponentFactoryRegistry] = None


def get_factory_registry() -> ComponentFactoryRegistry:
    """Get the global factory registry instance"""
    global _factory_registry
    if _factory_registry is None:
        _factory_registry = ComponentFactoryRegistry()
    return _factory_registry
