"""
Test suite for addon installer functionality

This module tests:
- AddonInstaller operations and lifecycle
- BaseModuleInstaller functionality
- Installation, upgrade, and uninstallation processes
- Hook execution during installation
- Error handling and rollback scenarios
"""
import pytest
from unittest.mock import MagicMock, patch

from erp.addons.installers import AddonInstaller, BaseModuleInstaller


class TestAddonInstaller:
    """Test AddonInstaller functionality"""
    
    def test_installer_initialization(self):
        """Test AddonInstaller initialization"""
        installer = AddonInstaller()

        assert hasattr(installer, 'logger')
        # Test that shared registry updater is initialized
        assert hasattr(installer, '_registry_updater')
    
    @pytest.mark.asyncio
    async def test_install_addon_success(self):
        """Test successful addon installation"""
        installer = AddonInstaller()
        
        # Mock environment
        mock_env = MagicMock()
        
        with patch.object(installer, '_execute_hooks', return_value=True) as mock_execute:
            result = await installer.install_addon('test_addon', mock_env)
            
            assert result is True
            # Should execute pre-install and post-install hooks
            assert mock_execute.call_count >= 2
    
    @pytest.mark.asyncio
    async def test_install_addon_hook_failure(self):
        """Test addon installation with hook failure"""
        installer = AddonInstaller()
        
        mock_env = MagicMock()
        
        with patch.object(installer, '_execute_hooks', return_value=False):
            result = await installer.install_addon('test_addon', mock_env)
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_uninstall_addon_success(self):
        """Test successful addon uninstallation"""
        installer = AddonInstaller()
        
        mock_env = MagicMock()
        
        with patch.object(installer, '_execute_hooks', return_value=True):
            result = await installer.uninstall_addon('test_addon', mock_env)
            
            assert result is True
    
    @pytest.mark.asyncio
    async def test_upgrade_addon_success(self):
        """Test successful addon upgrade"""
        installer = AddonInstaller()
        
        mock_env = MagicMock()
        
        with patch.object(installer, '_execute_hooks', return_value=True):
            result = await installer.upgrade_addon('test_addon', mock_env)
            
            assert result is True


class TestBaseModuleInstaller:
    """Test BaseModuleInstaller functionality"""
    
    def test_base_installer_initialization(self):
        """Test BaseModuleInstaller initialization"""
        installer = BaseModuleInstaller()

        assert hasattr(installer, 'logger')
        # Test that shared components are initialized
        assert hasattr(installer, '_registry_updater')
        assert hasattr(installer, '_validation_manager')
    
    @pytest.mark.asyncio
    async def test_install_base_module_success(self):
        """Test successful base module installation"""
        installer = BaseModuleInstaller()
        
        mock_env = MagicMock()
        
        with patch.object(installer, '_create_base_tables', return_value=True):
            with patch.object(installer, '_populate_base_data', return_value=True):
                result = await installer.install_base_module(mock_env)
                
                assert result is True
