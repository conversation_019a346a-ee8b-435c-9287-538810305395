"""
Schema comparison utilities
Contains logic for comparing model schemas with database tables
"""
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from ...logging import get_logger
from .generator import SchemaGenerator
from .types import camel_to_snake_case

logger = get_logger(__name__)


class SchemaComparator:
    """Utility for comparing model schemas with database tables"""

    @classmethod
    async def get_table_schema(cls, table_name: str) -> Optional[Dict[str, Any]]:
        """
        Get actual table schema from database

        Args:
            table_name: Name of the database table

        Returns:
            Dictionary containing actual table schema or None if table doesn't exist
        """
        from ...database.registry import DatabaseRegistry

        if not DatabaseRegistry:
            raise RuntimeError("Database not available")

        db = await DatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")

        # Check if table exists
        exists_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = $1
            )
        """
        result = await db.execute(exists_query, (table_name,))
        if not result or not result[0]['exists']:
            return None

        # Get column information
        columns_query = """
            SELECT
                column_name,
                data_type,
                is_nullable,
                column_default,
                character_maximum_length
            FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = $1
            ORDER BY ordinal_position
        """
        columns = await db.execute(columns_query, (table_name,))

        # Get indexes
        indexes_query = """
            SELECT
                indexname,
                indexdef
            FROM pg_indexes
            WHERE tablename = $1 AND schemaname = 'public'
        """
        indexes = await db.execute(indexes_query, (table_name,))

        # Get constraints
        constraints_query = """
            SELECT
                conname,
                contype,
                pg_get_constraintdef(oid) as definition
            FROM pg_constraint
            WHERE conrelid = $1::regclass
        """
        constraints = await db.execute(constraints_query, (table_name,))

        schema = {
            'table_name': table_name,
            'columns': {col['column_name']: col for col in columns},
            'indexes': [idx['indexname'] for idx in indexes],
            'constraints': [{'name': c['conname'], 'type': c['contype'], 'definition': c['definition']} for c in constraints]
        }

        return schema

    @classmethod
    async def compare_model_with_table(cls, model_name: str) -> Dict[str, Any]:
        """
        Compare a model definition with its database table

        Args:
            model_name: Name of the model to compare

        Returns:
            Dictionary containing comparison results
        """
        try:
            # Get model schema
            model_schema = SchemaGenerator.get_model_schema(model_name)
            if not model_schema:
                return {
                    'status': 'error',
                    'message': f'Could not get schema for model: {model_name}'
                }

            # Get table schema
            table_name = model_schema['table_name']
            table_schema = await cls.get_table_schema(table_name)
            
            if not table_schema:
                return {
                    'status': 'missing_table',
                    'message': f'Table {table_name} does not exist for model {model_name}'
                }

            # Compare schemas
            differences = cls._compare_schemas(model_schema, table_schema)
            
            if not differences:
                return {
                    'status': 'match',
                    'message': f'Model {model_name} matches table {table_name}'
                }
            else:
                return {
                    'status': 'differences',
                    'message': f'Found differences between model {model_name} and table {table_name}',
                    'differences': differences
                }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'Error comparing model {model_name}: {e}'
            }

    @classmethod
    def _compare_schemas(cls, model_schema: Dict[str, Any], table_schema: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Compare model schema with table schema and return differences

        Args:
            model_schema: Schema from model definition
            table_schema: Schema from database table

        Returns:
            List of differences found
        """
        differences = []
        
        model_fields = model_schema.get('fields', {})
        table_columns = table_schema.get('columns', {})

        # Check for missing columns
        for field_name, field_info in model_fields.items():
            db_column_name = camel_to_snake_case(field_name)
            
            if db_column_name not in table_columns:
                differences.append({
                    'type': 'missing_column',
                    'field': field_name,
                    'column': db_column_name,
                    'expected_type': field_info.get('type')
                })

        # Check for extra columns
        expected_columns = {camel_to_snake_case(f) for f in model_fields.keys()}
        for column_name in table_columns.keys():
            if column_name not in expected_columns:
                differences.append({
                    'type': 'extra_column',
                    'column': column_name,
                    'actual_type': table_columns[column_name].get('data_type')
                })

        # Check for type mismatches
        for field_name, field_info in model_fields.items():
            db_column_name = camel_to_snake_case(field_name)
            
            if db_column_name in table_columns:
                expected_type = field_info.get('type', '').upper()
                actual_type = table_columns[db_column_name].get('data_type', '').upper()
                
                # Normalize type names for comparison
                if not cls._types_match(expected_type, actual_type):
                    differences.append({
                        'type': 'type_mismatch',
                        'field': field_name,
                        'column': db_column_name,
                        'expected_type': expected_type,
                        'actual_type': actual_type
                    })

        return differences

    @classmethod
    def _types_match(cls, expected_type: str, actual_type: str) -> bool:
        """
        Check if expected and actual types match, accounting for PostgreSQL type variations

        Args:
            expected_type: Expected PostgreSQL type
            actual_type: Actual PostgreSQL type from database

        Returns:
            True if types match, False otherwise
        """
        # Normalize types
        expected = expected_type.upper().strip()
        actual = actual_type.upper().strip()

        # Direct match
        if expected == actual:
            return True

        # Handle VARCHAR variations
        if expected.startswith('VARCHAR') and actual == 'CHARACTER VARYING':
            return True
        
        if expected == 'TEXT' and actual == 'TEXT':
            return True
            
        if expected == 'UUID' and actual == 'UUID':
            return True
            
        if expected == 'BOOLEAN' and actual == 'BOOLEAN':
            return True
            
        if expected == 'INTEGER' and actual == 'INTEGER':
            return True
            
        if expected == 'REAL' and (actual == 'REAL' or actual == 'DOUBLE PRECISION'):
            return True
            
        if expected == 'TIMESTAMP' and actual.startswith('TIMESTAMP'):
            return True
            
        if expected == 'DATE' and actual == 'DATE':
            return True
            
        if expected == 'JSONB' and actual == 'JSONB':
            return True
            
        if expected == 'BYTEA' and actual == 'BYTEA':
            return True

        return False

    @classmethod
    async def sync_model_tables(cls, addon_name: str = None, model_registry: 'ModelRegistry' = None) -> Dict[str, Any]:
        """
        Synchronize model tables with their definitions
        Creates missing tables and reports differences

        Enhanced version with better error handling and completion detection
        to ensure IR population only happens after successful sync.

        Args:
            addon_name: Optional addon name to sync only models from specific addon
            model_registry: Optional ModelRegistry instance to use (for lifecycle-bound operations)

        Returns:
            Dictionary containing sync results and statistics with clear success/failure status
        """
        from ...database.registry import DatabaseRegistry

        try:
            db = await DatabaseRegistry.get_current_database()
            if not db:
                return {
                    'status': 'error',
                    'message': 'No database connection available',
                    'sync_successful': False,
                    'errors': ['No database connection available']
                }

            # Use provided model registry or create one for the specific addon
            if model_registry:
                models = model_registry.all()
                logger.info(f"Using provided ModelRegistry with {len(models)} models")
            elif addon_name:
                from ...addons.lifecycle_manager import get_addon_lifecycle_manager
                lifecycle_manager = get_addon_lifecycle_manager()
                temp_registry = lifecycle_manager.create_temporary_model_registry(addon_name)
                models = temp_registry.all()
                logger.info(f"Created temporary ModelRegistry for addon '{addon_name}' with {len(models)} models")
            else:
                # Fallback: process base models only
                models = {}
                logger.warning("No addon specified and no model registry provided - no models to sync")

            results = {
                'status': 'success',
                'sync_successful': True,
                'total_models': len(models),
                'tables_created': 0,
                'tables_updated': 0,
                'tables_in_sync': 0,
                'errors': [],
                'warnings': [],
                'details': {},
                'addon_name': addon_name
            }

            # Track critical errors that should prevent IR population
            critical_errors = []

            for model_name in models.keys():
                try:
                    # Check if table exists
                    table_name = models[model_name]._table or model_name.replace('.', '_')
                    table_exists = await db.fetchval(
                        """SELECT EXISTS (
                           SELECT FROM information_schema.tables
                           WHERE table_schema = 'public'
                           AND table_name = $1
                        )""",
                        table_name
                    )

                    if not table_exists:
                        # Create missing table
                        sql = SchemaGenerator.generate_create_table_sql(model_name)
                        if sql:
                            try:
                                await db.execute(sql)
                                results['tables_created'] += 1
                                results['details'][model_name] = 'created'
                                logger.debug(f"✓ Created table for model {model_name}")
                            except Exception as create_error:
                                error_msg = f"Failed to create table for {model_name}: {create_error}"
                                critical_errors.append(error_msg)
                                results['errors'].append(error_msg)
                                results['details'][model_name] = 'creation_failed'
                        else:
                            error_msg = f"Failed to generate SQL for {model_name}"
                            critical_errors.append(error_msg)
                            results['errors'].append(error_msg)
                            results['details'][model_name] = 'sql_generation_failed'
                    else:
                        # Compare existing table with model definition
                        comparison = await cls.compare_model_with_table(model_name)
                        if comparison.get('status') == 'match':
                            results['tables_in_sync'] += 1
                            results['details'][model_name] = 'in_sync'
                        else:
                            results['tables_updated'] += 1
                            results['details'][model_name] = 'differences_found'
                            # Note: Actual table updates would require more complex logic
                            # For now, we just report the differences as warnings
                            results['warnings'].append(f"Table differences found for {model_name}")

                except Exception as e:
                    error_msg = f"Error processing model {model_name}: {e}"
                    critical_errors.append(error_msg)
                    results['errors'].append(error_msg)
                    results['details'][model_name] = 'error'

            # Determine if sync was successful
            if critical_errors:
                results['status'] = 'error'
                results['sync_successful'] = False
                logger.error(f"Schema sync failed with {len(critical_errors)} critical errors")
            elif results['errors']:
                results['status'] = 'partial'
                results['sync_successful'] = False
                logger.warning(f"Schema sync completed with {len(results['errors'])} errors")
            else:
                results['sync_successful'] = True
                logger.info(f"Schema sync completed successfully: {results['tables_created']} created, "
                          f"{results['tables_in_sync']} in sync, {results['tables_updated']} updated")

            # Clean up temporary registry if we created one
            if not model_registry and addon_name:
                from ...addons.lifecycle_manager import get_addon_lifecycle_manager
                lifecycle_manager = get_addon_lifecycle_manager()
                lifecycle_manager.cleanup_temporary_model_registry(addon_name)

            return results

        except Exception as e:
            error_msg = f"Error during table synchronization: {e}"
            logger.error(error_msg)

            # Clean up temporary registry if we created one
            if not model_registry and addon_name:
                try:
                    from ...addons.lifecycle_manager import get_addon_lifecycle_manager
                    lifecycle_manager = get_addon_lifecycle_manager()
                    lifecycle_manager.cleanup_temporary_model_registry(addon_name)
                except Exception as cleanup_error:
                    logger.warning(f"Failed to cleanup temporary registry for {addon_name}: {cleanup_error}")

            return {
                'status': 'error',
                'sync_successful': False,
                'message': error_msg,
                'errors': [error_msg],
                'addon_name': addon_name
            }

    @classmethod
    async def validate_database_schema(cls, model_registry: 'ModelRegistry' = None) -> Dict[str, Any]:
        """
        Validate that all registered models have corresponding database tables
        with correct structure

        Args:
            model_registry: Optional ModelRegistry instance to validate

        Returns:
            Dictionary containing validation results
        """
        from ...database.registry import DatabaseRegistry

        try:
            db = await DatabaseRegistry.get_current_database()
            if not db:
                return {
                    'status': 'error',
                    'message': 'No database connection available'
                }

            # Use provided model registry or return empty validation
            if model_registry:
                models = model_registry.all()
            else:
                models = {}
                logger.warning("No model registry provided for validation")

            results = {
                'status': 'success',
                'total_models': len(models),
                'valid_tables': 0,
                'missing_tables': 0,
                'invalid_tables': 0,
                'issues': [],
                'details': {}
            }

            for model_name in models.keys():
                try:
                    table_name = models[model_name]._table or model_name.replace('.', '_')

                    # Check if table exists
                    table_exists = await db.fetchval(
                        """SELECT EXISTS (
                           SELECT FROM information_schema.tables
                           WHERE table_schema = 'public'
                           AND table_name = $1
                        )""",
                        table_name
                    )

                    if not table_exists:
                        results['missing_tables'] += 1
                        results['details'][model_name] = 'missing'
                        results['issues'].append(f"Table {table_name} missing for model {model_name}")
                    else:
                        # Validate table structure
                        comparison = await cls.compare_model_with_table(model_name)
                        if comparison.get('status') == 'match':
                            results['valid_tables'] += 1
                            results['details'][model_name] = 'valid'
                        else:
                            results['invalid_tables'] += 1
                            results['details'][model_name] = 'invalid'
                            results['issues'].append(f"Table structure issues for {model_name}: {comparison.get('message', 'Unknown')}")

                except Exception as e:
                    results['invalid_tables'] += 1
                    results['details'][model_name] = 'error'
                    results['issues'].append(f"Error validating {model_name}: {e}")

            # Determine overall status
            if results['missing_tables'] > 0 or results['invalid_tables'] > 0:
                results['status'] = 'issues_found'

            return results

        except Exception as e:
            return {
                'status': 'error',
                'message': f'Error during schema validation: {e}'
            }

    @classmethod
    async def sync_and_populate_ir_metadata(cls, db_manager, addon_name: str = None) -> Dict[str, Any]:
        """
        Comprehensive method that performs schema sync followed by IR population.

        This method ensures that IR population only happens after successful schema sync.
        It's designed to be used during addon install/upgrade processes.

        Args:
            db_manager: Database manager instance
            addon_name: Optional addon name for targeted sync/population

        Returns:
            Dictionary containing both sync and population results
        """
        from ...logging.coordination import operation_context

        operation_id = f"schema_sync_ir_{addon_name or 'all'}"
        with operation_context(operation_id, addon=addon_name) as should_execute:
            if not should_execute:
                logger.debug(f"Skipping duplicate schema sync and IR population for {addon_name}")
                return {'status': 'skipped', 'message': 'Duplicate operation prevented'}

            logger.info(f"Starting schema sync and IR population for addon: {addon_name or 'all'}")

            try:
                # Step 1: Perform schema synchronization
                logger.info("Step 1: Synchronizing database schema...")
                sync_results = await cls.sync_model_tables(addon_name)

                # Step 2: Check if schema sync was successful
                if not sync_results.get('sync_successful', False):
                    logger.error("Schema synchronization failed - aborting IR population")
                    return {
                        'status': 'error',
                        'message': 'Schema synchronization failed',
                        'schema_sync': sync_results,
                        'ir_population': {
                            'status': 'skipped',
                            'message': 'Skipped due to schema sync failure'
                        }
                    }

                logger.info("✓ Schema synchronization completed successfully")

                # Step 3: Perform IR metadata population
                logger.info("Step 2: Populating IR metadata...")
                from ..ir.population_manager import ir_population_manager

                ir_results = await ir_population_manager.populate_ir_metadata(
                    db_manager, addon_name, sync_results
                )

                # Step 4: Compile final results
                overall_status = 'success'
                if sync_results.get('status') == 'error' or ir_results.get('status') == 'error':
                    overall_status = 'error'
                elif sync_results.get('status') == 'partial' or ir_results.get('status') == 'partial':
                    overall_status = 'partial'

                final_results = {
                    'status': overall_status,
                    'message': 'Schema sync and IR population completed',
                    'addon_name': addon_name,
                    'schema_sync': sync_results,
                    'ir_population': ir_results,
                    'summary': {
                        'tables_created': sync_results.get('tables_created', 0),
                        'tables_in_sync': sync_results.get('tables_in_sync', 0),
                        'models_populated': ir_results.get('models_processed', 0),
                        'fields_populated': ir_results.get('fields_processed', 0)
                    }
                }

                logger.info(f"✅ Schema sync and IR population completed: "
                          f"{final_results['summary']['tables_created']} tables created, "
                          f"{final_results['summary']['models_populated']} models populated")

                return final_results

            except Exception as e:
                error_msg = f"Error during schema sync and IR population: {e}"
                logger.error(error_msg)
                return {
                    'status': 'error',
                    'message': error_msg,
                    'addon_name': addon_name,
                    'schema_sync': {'status': 'error', 'message': 'Not attempted'},
                    'ir_population': {'status': 'error', 'message': 'Not attempted'}
                }
