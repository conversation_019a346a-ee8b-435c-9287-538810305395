"""
Command utilities for relational fields
"""
from .base import FieldValidationError


class Command:
    """Command constants for One2many and Many2many operations"""
    CREATE = 0    # (0, 0, values) - create new record
    UPDATE = 1    # (1, id, values) - update existing record
    DELETE = 2    # (2, id, 0) - delete record
    UNLINK = 3    # (3, id, 0) - unlink record (don't delete)
    LINK = 4      # (4, id, 0) - link existing record
    CLEAR = 5     # (5, 0, 0) - clear all links
    SET = 6       # (6, 0, [ids]) - replace all links with given ids

    @classmethod
    def create(cls, values):
        """Create a CREATE command"""
        return (cls.CREATE, 0, values)

    @classmethod
    def update(cls, record_id, values):
        """Create an UPDATE command"""
        return (cls.UPDATE, record_id, values)

    @classmethod
    def delete(cls, record_id):
        """Create a DELETE command"""
        return (cls.DELETE, record_id, 0)

    @classmethod
    def unlink(cls, record_id):
        """Create an UNLINK command"""
        return (cls.UNLINK, record_id, 0)

    @classmethod
    def link(cls, record_id):
        """Create a LINK command"""
        return (cls.LINK, record_id, 0)

    @classmethod
    def clear(cls):
        """Create a CLEAR command"""
        return (cls.CLEAR, 0, 0)

    @classmethod
    def set(cls, record_ids):
        """Create a SET command"""
        return (cls.SET, 0, record_ids)

    @classmethod
    def code_to_name(cls, code):
        """Convert command code to name"""
        names = {
            0: 'CREATE',
            1: 'UPDATE',
            2: 'DELETE',
            3: 'UNLINK',
            4: 'LINK',
            5: 'CLEAR',
            6: 'SET'
        }
        return names.get(code, 'UNKNOWN')


# Utility functions for relational fields

def get_intersection_table_name(model1_name, model2_name, relation_name=None):
    """Generate intersection table name for Many2many relationships"""
    if relation_name:
        return relation_name

    # Auto-generate table name: model1_model2_rel
    model1 = model1_name.replace('.', '_')
    model2 = model2_name.replace('.', '_')

    # Ensure consistent ordering for bidirectional relationships
    if model1 < model2:
        return f"{model1}_{model2}_rel"
    else:
        return f"{model2}_{model1}_rel"


def validate_relational_command(command):
    """Validate relational field command tuple"""
    if not isinstance(command, (list, tuple)) or len(command) < 3:
        raise FieldValidationError("Command must be a tuple/list with at least 3 elements")

    cmd_code = command[0]
    if cmd_code not in (0, 1, 2, 3, 4, 5, 6):
        raise FieldValidationError(f"Invalid command code: {cmd_code}")

    # Validate command structure based on code
    if cmd_code == 0:  # CREATE
        # (0, 0, {values})
        if not isinstance(command[2], dict):
            raise FieldValidationError("CREATE command requires a dictionary of values")

    elif cmd_code == 1:  # UPDATE
        # (1, id, {values})
        if not command[1]:
            raise FieldValidationError("UPDATE command requires a valid ID")
        if not isinstance(command[2], dict):
            raise FieldValidationError("UPDATE command requires a dictionary of values")

    elif cmd_code in (2, 3, 4):  # DELETE, UNLINK, LINK
        # (2/3/4, id, 0)
        if not command[1]:
            raise FieldValidationError(f"{Command.code_to_name(cmd_code)} command requires a valid ID")

    elif cmd_code == 5:  # CLEAR
        # (5, 0, 0)
        pass

    elif cmd_code == 6:  # SET
        # (6, 0, [ids])
        if not isinstance(command[2], (list, tuple)):
            raise FieldValidationError("SET command requires a list of IDs")

    return True


async def check_relational_integrity(field, value, model_registry=None):
    """
    Check relational integrity for a field value

    Args:
        field: The field object
        value: The value to check
        model_registry: Optional model registry for checking record existence

    Returns:
        True if valid, raises exception otherwise
    """
    from .relational import Many2One, One2One

    if isinstance(field, (Many2One, One2One)) and value:
        # Check if referenced record exists
        if model_registry:
            comodel = model_registry.get_model(field.comodel_name)
            if comodel:
                record_exists = await comodel.exists(value)
                if not record_exists:
                    raise FieldValidationError(
                        f"Referenced record {value} does not exist in {field.comodel_name}"
                    )

    return True
