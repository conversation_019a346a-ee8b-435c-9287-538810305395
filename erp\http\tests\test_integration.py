"""
Integration tests for the HTTP module
Tests basic functionality after legacy cleanup
"""

import pytest

from ..interfaces import RouteInfo, RouteScope
from ..metadata import RouteType, AuthType
from ..registries import get_system_route_registry, reset_system_route_registry
from ..services import get_route_info_factory
from ..middleware import get_middleware_pipeline
from ..controllers import Controller, get_controller_registry
from ..decorators import route, systemRoute


class TestHTTPIntegration:
    """Test HTTP system integration"""
    
    @pytest.fixture(autouse=True)
    async def setup_and_teardown(self):
        """Setup and teardown for each test"""
        # Reset registries before each test
        await reset_system_route_registry()
        get_controller_registry().clear()
        
        yield
        
        # Cleanup after each test
        await reset_system_route_registry()
        get_controller_registry().clear()
    
    @pytest.mark.asyncio
    async def test_basic_route_registration(self):
        """Test basic route registration"""
        registry = get_system_route_registry()
        factory = get_route_info_factory()
        
        # Create a simple route
        route_info = factory.create_route_info(
            path='/test',
            handler=lambda: "test",
            methods=['GET'],
            scope=RouteScope.SYSTEM
        )
        
        # Register the route
        await registry.register_route(route_info)
        
        # Check it was registered
        routes = await registry.get_routes()
        assert '/test' in [r.path for r in routes]
    
    @pytest.mark.asyncio
    async def test_route_decorator(self):
        """Test route decorator functionality"""
        
        @route('/decorated-route', type=RouteType.HTTP, auth=AuthType.PUBLIC, methods=['GET'])
        async def test_handler(request):
            return "decorated route"
        
        # Verify it has the expected metadata
        assert hasattr(test_handler, '_route_metadata')
        metadata = test_handler._route_metadata
        assert metadata['path'] == '/decorated-route'
        assert metadata['type'] == RouteType.HTTP
        assert metadata['auth'] == AuthType.PUBLIC
    
    def test_controller_functionality(self):
        """Test controller functionality"""
        class TestController(Controller):
            def test_method(self):
                return "controller works"
        
        controller = TestController()
        result = controller.test_method()
        assert result == "controller works"


class TestImports:
    """Test that all main imports work"""
    
    def test_main_imports(self):
        """Test that main imports work"""
        try:
            from .. import route, systemRoute, Controller
            from .. import JsonRpcHandler, AuthType
            
            assert route is not None
            assert systemRoute is not None
            assert Controller is not None
            assert JsonRpcHandler is not None
            assert AuthType is not None
            
        except ImportError as e:
            pytest.fail(f"Import failed: {e}")
    
    def test_middleware_imports(self):
        """Test middleware imports"""
        try:
            from ..middleware import MiddlewarePipeline, BaseMiddleware, CorsMiddleware
            from ..middleware import RequestProcessingMiddleware, ResponseProcessingMiddleware
            
            assert MiddlewarePipeline is not None
            assert BaseMiddleware is not None
            assert CorsMiddleware is not None
            assert RequestProcessingMiddleware is not None
            assert ResponseProcessingMiddleware is not None
            
        except ImportError as e:
            pytest.fail(f"Middleware import failed: {e}")
    
    def test_auth_imports(self):
        """Test auth imports"""
        try:
            from ..auth import AuthType, require_auth, AuthMiddleware
            
            assert AuthType is not None
            assert require_auth is not None
            assert AuthMiddleware is not None
            
        except ImportError as e:
            pytest.fail(f"Auth import failed: {e}")
