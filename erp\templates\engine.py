"""
Template Engine - Core rendering functionality
"""
import xml.etree.ElementTree as ET
from typing import Dict, Any, List, Optional, Union
import html
import re
import time
from .parser import Template<PERSON>ars<PERSON>, ExpressionEvaluator, LoopContext
from .exceptions import TemplateRenderError, TemplateSyntaxError
from .cache import CacheManager, CompiledTemplate, get_cache_manager
from .debug import TemplateDebug<PERSON>, DebugContext, get_template_debugger, format_template_error
from .security import SecurityManager, get_security_manager


class TemplateEngine:
    """Core template rendering engine"""

    def __init__(self, enable_caching: bool = True, enable_debugging: bool = False, enable_security: bool = True):
        self.parser = TemplateParser()
        self.evaluator = ExpressionEvaluator()
        self.system_templates: Dict[str, ET.Element] = {}  # Renamed from templates to system_templates
        self.enable_caching = enable_caching
        self.cache_manager = get_cache_manager() if enable_caching else None
        self.enable_debugging = enable_debugging
        self.debugger = get_template_debugger() if enable_debugging else None
        self.enable_security = enable_security
        self.security_manager = get_security_manager() if enable_security else None
    
    def load_template(self, template_name: str, template_content: str):
        """Load a template from content"""
        templates = self.parser.parse_template_file(template_content)
        self.system_templates.update(templates)
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render a template with given context"""
        if template_name not in self.system_templates:
            raise TemplateRenderError(f"Template '{template_name}' not found")

        # Security validation
        if self.enable_security and self.security_manager:
            self.security_manager.check_context_safety(context)

        # Check render cache first
        if self.enable_caching and self.cache_manager:
            cached_output = self.cache_manager.get_rendered_output(template_name, context)
            if cached_output is not None:
                return cached_output

        # Measure rendering time
        start_time = time.time()

        template_element = self.system_templates[template_name]
        result = self._render_element(template_element, context)

        # Record performance metrics
        render_time = time.time() - start_time
        if self.enable_caching and self.cache_manager:
            context_size = len(str(context))
            self.cache_manager.performance_monitor.record_render(
                template_name, render_time, context_size
            )

            # Cache the result if it's not too large
            if len(result) < 100000:  # Don't cache very large outputs
                self.cache_manager.set_rendered_output(template_name, context, result)

        return result
    
    def _render_element(self, element: ET.Element, context: Dict[str, Any]) -> str:
        """Render a single XML element"""
        # Handle template control elements (t tags)
        if self.parser.is_template_element(element):
            return self._render_template_element(element, context)
        
        # Handle regular elements with directives
        if self.parser.has_directives(element):
            return self._render_element_with_directives(element, context)
        
        # Handle regular elements without directives
        return self._render_regular_element(element, context)
    
    def _render_template_element(self, element: ET.Element, context: Dict[str, Any]) -> str:
        """Render template control element (t tag)"""
        directives = self.parser.extract_directives(element)

        # Handle t-ignore first
        if 't-ignore' in directives:
            ignore_value = directives['t-ignore']
            if ignore_value.lower() in ('true', '1') or self.evaluator.evaluate_condition(ignore_value, context):
                return ""

        # Handle conditional rendering
        if not self._should_render_conditionally(directives, context):
            return ""

        # Handle t-foreach
        if 't-foreach' in directives:
            return self._render_foreach(element, directives, context)

        # Handle t-set
        if 't-set' in directives:
            return self._render_set(element, directives, context)

        # Handle t-include
        if 't-include' in directives:
            return self._render_include(element, directives, context)

        # Handle t-slot
        if 't-slot' in directives:
            return self._render_slot(element, directives, context)

        # Handle t-extend
        if 't-extend' in directives:
            return self._render_extend(element, directives, context)

        # Handle t-block
        if 't-block' in directives:
            return self._render_block(element, directives, context)

        # Handle t-super
        if 't-super' in directives:
            return self._render_super(element, directives, context)

        # Handle debugging directives
        if 't-debug' in directives:
            return self._render_debug(element, directives, context)

        if 't-log' in directives:
            return self._render_log(element, directives, context)

        # Handle content directives
        if 't-esc' in directives:
            value = self.evaluator.evaluate(directives['t-esc'], context)
            return html.escape(str(value))

        if 't-raw' in directives:
            value = self.evaluator.evaluate(directives['t-raw'], context)
            return str(value)

        if 't-field' in directives:
            value = self.evaluator.evaluate(directives['t-field'], context)
            return html.escape(str(value))

        # Render children
        return self._render_children(element, context)

    def _should_render_conditionally(self, directives: Dict[str, str], context: Dict[str, Any]) -> bool:
        """Check if element should be rendered based on conditional directives"""
        if 't-if' in directives:
            condition = directives['t-if']
            return self.evaluator.evaluate_condition(condition, context)
        elif 't-elif' in directives:
            condition = directives['t-elif']
            return self.evaluator.evaluate_condition(condition, context)
        elif 't-else' in directives:
            # t-else is always true when reached
            return True

        return True  # No conditional directive, render by default
    
    def _render_element_with_directives(self, element: ET.Element, context: Dict[str, Any]) -> str:
        """Render regular element that has template directives"""
        directives = self.parser.extract_directives(element)

        # Handle t-ignore first
        if 't-ignore' in directives:
            ignore_value = directives['t-ignore']
            if ignore_value.lower() in ('true', '1') or self.evaluator.evaluate_condition(ignore_value, context):
                return ""

        # Handle conditional rendering
        if not self._should_render_conditionally(directives, context):
            return ""

        # Handle t-foreach on regular elements
        if 't-foreach' in directives:
            return self._render_foreach_element(element, directives, context)

        # Start building element
        tag_name = element.tag
        attributes = self._process_element_attributes(element, directives, context)

        # Build opening tag
        attr_str = ' '.join(f'{name}="{html.escape(value)}"' for name, value in attributes.items())
        opening_tag = f"<{tag_name}" + (f" {attr_str}" if attr_str else "") + ">"

        # Handle content
        content = self._render_element_content(element, directives, context)

        # Build closing tag
        closing_tag = f"</{tag_name}>"

        return opening_tag + content + closing_tag

    def _process_element_attributes(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> Dict[str, str]:
        """Process element attributes including dynamic ones"""
        attributes = {}

        for attr_name, attr_value in element.attrib.items():
            if attr_name.startswith('t-'):
                # Handle template directives
                if attr_name.startswith('t-att-'):
                    # Dynamic attribute
                    real_attr_name = attr_name[6:]  # Remove 't-att-' prefix
                    try:
                        evaluated_value = self.evaluator.evaluate(attr_value, context)
                        attributes[real_attr_name] = str(evaluated_value)
                    except Exception as e:
                        raise TemplateRenderError(f"Error evaluating t-att-{real_attr_name}: {e}")
                elif attr_name == 't-att-class':
                    # Special handling for class attribute
                    try:
                        evaluated_value = self.evaluator.evaluate(attr_value, context)
                        attributes['class'] = str(evaluated_value)
                    except Exception as e:
                        raise TemplateRenderError(f"Error evaluating t-att-class: {e}")
                elif attr_name == 't-att-style':
                    # Special handling for style attribute
                    try:
                        evaluated_value = self.evaluator.evaluate(attr_value, context)
                        attributes['style'] = str(evaluated_value)
                    except Exception as e:
                        raise TemplateRenderError(f"Error evaluating t-att-style: {e}")
                # Skip other directives (they're handled elsewhere)
            else:
                # Regular attribute
                attributes[attr_name] = attr_value

        return attributes

    def _render_element_content(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Render element content based on directives"""
        # Handle t-esc (escaped content)
        if 't-esc' in directives:
            value = self.evaluator.evaluate(directives['t-esc'], context)
            return html.escape(str(value))

        # Handle t-raw (raw HTML content)
        elif 't-raw' in directives:
            value = self.evaluator.evaluate(directives['t-raw'], context)
            raw_content = str(value)

            # Apply security sanitization if enabled
            if self.enable_security and self.security_manager:
                raw_content = self.security_manager.sanitize_output(raw_content, 'html')

            return raw_content

        # Handle t-field (field content - simplified)
        elif 't-field' in directives:
            value = self.evaluator.evaluate(directives['t-field'], context)
            return html.escape(str(value))

        else:
            # Render children and text content
            content = ""
            if element.text:
                # Don't escape content for script, style, and other raw content tags
                if element.tag.lower() in ['script', 'style', 'pre', 'code', 'textarea']:
                    content += element.text
                else:
                    content += html.escape(element.text)
            content += self._render_children(element, context)
            if element.tail:
                content += html.escape(element.tail)
            return content
    
    def _render_regular_element(self, element: ET.Element, context: Dict[str, Any]) -> str:
        """Render regular element without directives"""
        tag_name = element.tag

        # Build attributes
        attr_str = ' '.join(f'{name}="{html.escape(value)}"' for name, value in element.attrib.items())
        opening_tag = f"<{tag_name}" + (f" {attr_str}" if attr_str else "") + ">"

        # Build content
        content = ""
        if element.text:
            # Don't escape content for script, style, and other raw content tags
            if tag_name.lower() in ['script', 'style', 'pre', 'code', 'textarea']:
                content += element.text
            else:
                content += html.escape(element.text)
        content += self._render_children(element, context)

        closing_tag = f"</{tag_name}>"

        result = opening_tag + content + closing_tag

        # Add tail text if present
        if element.tail:
            result += html.escape(element.tail)

        return result
    
    def _render_children(self, element: ET.Element, context: Dict[str, Any]) -> str:
        """Render all child elements"""
        result = ""
        for child in element:
            result += self._render_element(child, context)
        return result

    def _render_foreach_element(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Render t-foreach on a regular element (not t tag)"""
        foreach_expr = directives['t-foreach']
        as_var = directives.get('t-as', 'item')

        # Evaluate the iterable
        try:
            items = self.evaluator.evaluate(foreach_expr, context)
            if not hasattr(items, '__iter__'):
                items = [items]
            items = list(items)
        except Exception as e:
            raise TemplateRenderError(f"Error in t-foreach: {e}")

        # Apply filtering if t-filter is present
        if 't-filter' in directives:
            items = self._filter_items(items, directives['t-filter'], as_var, context)

        # Apply sorting if t-sort is present
        if 't-sort' in directives:
            items = self._sort_items(items, directives['t-sort'], as_var, context)

        # Apply limit if t-limit is present
        if 't-limit' in directives:
            try:
                limit = int(self.evaluator.evaluate(directives['t-limit'], context))
                items = items[:limit]
            except (ValueError, TypeError):
                pass  # Ignore invalid limit

        # Render loop - each iteration renders the entire element
        result = ""
        loop_context = LoopContext(items, as_var)

        for i, item in enumerate(items):
            loop_context.index = i

            # Create new context with loop variables
            loop_ctx = context.copy()
            loop_ctx[as_var] = item
            loop_ctx.update(loop_context.loop_vars)

            # Create a copy of the element without the loop directives
            elem_copy = ET.Element(element.tag, element.attrib.copy())
            elem_copy.text = element.text
            elem_copy.tail = element.tail
            for child in element:
                elem_copy.append(child)

            # Remove loop directives from the copy
            for directive in ['t-foreach', 't-as', 't-filter', 't-sort', 't-limit']:
                if directive in elem_copy.attrib:
                    del elem_copy.attrib[directive]

            # Render the element for this iteration
            result += self._render_element(elem_copy, loop_ctx)

        return result

    def _render_foreach(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Render t-foreach loop"""
        foreach_expr = directives['t-foreach']
        as_var = directives.get('t-as', 'item')

        # Evaluate the iterable
        try:
            items = self.evaluator.evaluate(foreach_expr, context)
            if not hasattr(items, '__iter__'):
                items = [items]
            items = list(items)
        except Exception as e:
            raise TemplateRenderError(f"Error in t-foreach: {e}")

        # Apply filtering if t-filter is present
        if 't-filter' in directives:
            items = self._filter_items(items, directives['t-filter'], as_var, context)

        # Apply sorting if t-sort is present
        if 't-sort' in directives:
            items = self._sort_items(items, directives['t-sort'], as_var, context)

        # Apply limit if t-limit is present
        if 't-limit' in directives:
            try:
                limit = int(self.evaluator.evaluate(directives['t-limit'], context))
                items = items[:limit]
            except (ValueError, TypeError):
                pass  # Ignore invalid limit

        # Render loop
        result = ""
        loop_context = LoopContext(items, as_var)

        for i, item in enumerate(items):
            loop_context.index = i

            # Create new context with loop variables
            loop_ctx = context.copy()
            loop_ctx[as_var] = item
            loop_ctx.update(loop_context.loop_vars)

            # Render children for this iteration
            result += self._render_children(element, loop_ctx)

        return result
    
    def _render_set(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Handle t-set directive"""
        var_name = directives['t-set']
        
        if 't-value' in directives:
            # Set variable to evaluated expression
            value = self.evaluator.evaluate(directives['t-value'], context)
            context[var_name] = value
        else:
            # Set variable to rendered content of element
            content = self._render_children(element, context)
            context[var_name] = content
        
        return ""  # t-set doesn't produce output

    def _filter_items(self, items: List[Any], filter_expr: str, as_var: str, context: Dict[str, Any]) -> List[Any]:
        """Filter items based on expression"""
        filtered_items = []

        for item in items:
            # Create context with current item
            item_context = context.copy()
            item_context[as_var] = item

            try:
                # Evaluate filter condition
                if self.evaluator.evaluate_condition(filter_expr, item_context):
                    filtered_items.append(item)
            except Exception:
                # If evaluation fails, skip the item
                continue

        return filtered_items

    def _sort_items(self, items: List[Any], sort_expr: str, as_var: str, context: Dict[str, Any]) -> List[Any]:
        """Sort items based on expression"""
        try:
            # Parse sort expression (can be "field" or "field:desc" or "field:asc")
            sort_parts = sort_expr.split(':')
            sort_key_expr = sort_parts[0].strip()
            sort_reverse = len(sort_parts) > 1 and sort_parts[1].strip().lower() == 'desc'

            def get_sort_key(item):
                # Create context with current item
                item_context = context.copy()
                item_context[as_var] = item

                try:
                    return self.evaluator.evaluate(sort_key_expr, item_context)
                except Exception:
                    return ""  # Default value for failed evaluations

            return sorted(items, key=get_sort_key, reverse=sort_reverse)

        except Exception:
            # If sorting fails, return original items
            return items

    def _render_include(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Handle t-include directive"""
        include_template = directives['t-include']

        # Evaluate template name (could be a variable)
        try:
            template_name = self.evaluator.evaluate(include_template, context)
            template_name = str(template_name)
        except Exception:
            # If evaluation fails, use as literal string
            template_name = include_template

        # Check if template exists
        if template_name not in self.system_templates:
            # Try to load template if it's not found
            # This allows for dynamic template loading
            raise TemplateRenderError(f"Included template '{template_name}' not found")

        # Create context for included template
        include_context = context.copy()

        # Handle t-with directive for passing variables
        if 't-with' in directives:
            with_expr = directives['t-with']
            try:
                # Evaluate the with expression
                with_vars = self.evaluator.evaluate(with_expr, context)
                if isinstance(with_vars, dict):
                    include_context.update(with_vars)
                else:
                    # If not a dict, create a variable named 'value'
                    include_context['value'] = with_vars
            except Exception as e:
                raise TemplateRenderError(f"Error in t-with: {e}")

        # Handle individual variable assignments (t-set-* pattern)
        for attr_name, attr_value in directives.items():
            if attr_name.startswith('t-set-'):
                var_name = attr_name[6:]  # Remove 't-set-' prefix
                try:
                    include_context[var_name] = self.evaluator.evaluate(attr_value, context)
                except Exception as e:
                    raise TemplateRenderError(f"Error setting variable '{var_name}': {e}")

        # Render the included template
        included_template = self.system_templates[template_name]
        return self._render_element(included_template, include_context)

    def _render_slot(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Handle t-slot directive for template slots"""
        slot_name = directives['t-slot']

        # Check if there's slot content provided in context
        slot_content_key = f'__slot_{slot_name}'

        if slot_content_key in context:
            # Render provided slot content
            slot_content = context[slot_content_key]
            if isinstance(slot_content, str):
                return slot_content
            elif hasattr(slot_content, '__call__'):
                # If it's a callable, call it with current context
                return str(slot_content(context))
            else:
                return str(slot_content)
        else:
            # Render default slot content (children of this element)
            return self._render_children(element, context)

    def _render_extend(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Handle t-extend directive for template inheritance"""
        parent_template_name = directives['t-extend']

        # Evaluate template name (could be a variable)
        try:
            parent_template_name = self.evaluator.evaluate(parent_template_name, context)
            parent_template_name = str(parent_template_name)
        except Exception:
            # If evaluation fails, use as literal string
            pass

        # Check if parent template exists
        if parent_template_name not in self.system_templates:
            raise TemplateRenderError(f"Parent template '{parent_template_name}' not found for extension")

        # Collect blocks from the extending template
        blocks = self._collect_blocks(element)

        # Create context with blocks for inheritance
        extend_context = context.copy()
        extend_context['__blocks__'] = blocks
        extend_context['__extending__'] = True

        # Render the parent template with block overrides
        parent_template = self.system_templates[parent_template_name]
        return self._render_element(parent_template, extend_context)

    def _collect_blocks(self, element: ET.Element) -> Dict[str, ET.Element]:
        """Collect all t-block elements from the extending template"""
        blocks = {}

        def collect_recursive(elem):
            # Check if this element has t-block directive
            if elem.tag == 't' and 't-block' in elem.attrib:
                block_name = elem.attrib['t-block']
                blocks[block_name] = elem

            # Recursively collect from children
            for child in elem:
                collect_recursive(child)

        collect_recursive(element)
        return blocks

    def _render_block(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Handle t-block directive for template inheritance"""
        block_name = directives['t-block']

        # Check if we're in an extending context and have block overrides
        if context.get('__extending__') and '__blocks__' in context:
            blocks = context['__blocks__']
            if block_name in blocks:
                # Render the overriding block
                override_block = blocks[block_name]

                # Create context for the block with access to parent block content
                block_context = context.copy()
                block_context['__parent_block__'] = element
                block_context['__block_name__'] = block_name

                return self._render_children(override_block, block_context)

        # Render the default block content
        return self._render_children(element, context)

    def _render_super(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Handle t-super directive to render parent block content"""
        # Check if we have access to parent block
        if '__parent_block__' in context:
            parent_block = context['__parent_block__']

            # Create a clean context without the extending flags to avoid recursion
            super_context = context.copy()
            super_context.pop('__extending__', None)
            super_context.pop('__blocks__', None)
            super_context.pop('__parent_block__', None)
            super_context.pop('__block_name__', None)

            return self._render_children(parent_block, super_context)

        # If no parent block available, render nothing
        return ""

    def clear_cache(self) -> None:
        """Clear all template caches"""
        if self.cache_manager:
            self.cache_manager.clear_all()

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        if self.cache_manager:
            return self.cache_manager.get_stats()
        return {'caching_disabled': True}

    def set_cache_enabled(self, enabled: bool) -> None:
        """Enable or disable caching"""
        self.enable_caching = enabled
        if enabled and not self.cache_manager:
            self.cache_manager = get_cache_manager()
        elif not enabled:
            self.cache_manager = None

    def _render_debug(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Handle t-debug directive for debugging output"""
        if not self.enable_debugging or not self.debugger:
            return self._render_children(element, context)

        debug_expr = directives.get('t-debug', 'context')

        try:
            if debug_expr == 'context':
                # Debug entire context
                debug_output = f"<!-- DEBUG CONTEXT: {context.keys()} -->"
            else:
                # Debug specific expression
                value = self.evaluator.evaluate(debug_expr, context)
                debug_output = f"<!-- DEBUG {debug_expr}: {repr(value)} -->"
        except Exception as e:
            debug_output = f"<!-- DEBUG ERROR: {e} -->"

        # Render children and add debug output
        content = self._render_children(element, context)
        return debug_output + content

    def _render_log(self, element: ET.Element, directives: Dict[str, str], context: Dict[str, Any]) -> str:
        """Handle t-log directive for logging"""
        if not self.enable_debugging or not self.debugger:
            return self._render_children(element, context)

        log_expr = directives.get('t-log', 'element')

        try:
            if log_expr == 'element':
                message = f"Rendering element: {element.tag}"
            else:
                value = self.evaluator.evaluate(log_expr, context)
                message = f"Log: {log_expr} = {repr(value)}"

            # Log to debugger
            from .debug import DebugInfo
            from ..logging import get_logger
            logger = get_logger(__name__)

            debug_info = DebugInfo(
                template_name=context.get('__template_name__', 'unknown'),
                element_tag=element.tag,
                directive='t-log',
                expression=log_expr,
                context_vars={k: str(v)[:50] for k, v in context.items() if not k.startswith('__')}
            )
            self.debugger.log_debug(debug_info)

            logger.debug(f"Template log: {message}")

        except Exception as e:
            from ..logging import get_logger
            logger = get_logger(__name__)
            logger.debug(f"Template log error: {e}")

        # Render children normally
        return self._render_children(element, context)

    def set_debugging_enabled(self, enabled: bool) -> None:
        """Enable or disable debugging"""
        self.enable_debugging = enabled
        if enabled and not self.debugger:
            self.debugger = get_template_debugger()
        elif not enabled:
            self.debugger = None

    def get_debug_info(self) -> Dict[str, Any]:
        """Get debugging information"""
        if self.debugger:
            return {
                'enabled': self.enable_debugging,
                'debug_log_size': len(self.debugger.debug_log),
                'render_stack': self.debugger.get_render_stack(),
                'snapshots': len(self.debugger.context_snapshots)
            }
        return {'enabled': False}

    def set_security_enabled(self, enabled: bool) -> None:
        """Enable or disable security"""
        self.enable_security = enabled
        if enabled and not self.security_manager:
            self.security_manager = get_security_manager()
        elif not enabled:
            self.security_manager = None

    def get_security_info(self) -> Dict[str, Any]:
        """Get security information"""
        if self.security_manager:
            return self.security_manager.get_security_report()
        return {'enabled': False}

    def get_security_headers(self) -> Dict[str, str]:
        """Get security headers for HTTP responses"""
        if self.security_manager:
            return self.security_manager.get_security_headers()
        return {}

    def validate_template_security(self, template_content: str) -> List[str]:
        """Validate template content for security issues"""
        if self.security_manager:
            return self.security_manager.validate_template_content(template_content)
        return []
