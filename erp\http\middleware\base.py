"""
Base middleware implementation
Provides common functionality for middleware components
"""

from abc import <PERSON>
from typing import Any

from ..interfaces import IMiddleware, RouteInfo
from ...logging import get_logger


class BaseMiddleware(IMiddleware):
    """Base implementation of middleware interface"""
    
    def __init__(self, priority: int = 100):
        self.priority = priority
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
    
    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Default request processing - pass through"""
        return request
    
    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Default response processing - pass through"""
        return response
    
    def get_priority(self) -> int:
        """Get middleware priority"""
        return self.priority


class LoggingMiddleware(BaseMiddleware):
    """Middleware for logging requests and responses"""
    
    def __init__(self, priority: int = 10):
        super().__init__(priority)
    
    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Log incoming request"""
        self.logger.debug(f"Processing request: {route_info.methods} {route_info.path}")
        return request
    
    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Log outgoing response"""
        self.logger.debug(f"Processed response for: {route_info.methods} {route_info.path}")
        return response


class TimingMiddleware(BaseMiddleware):
    """Middleware for timing request processing"""
    
    def __init__(self, priority: int = 5):
        super().__init__(priority)
        self._start_times = {}
    
    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Record start time"""
        import time
        request_id = id(request)
        self._start_times[request_id] = time.time()
        return request
    
    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Calculate and log processing time"""
        import time
        request_id = id(response)  # Assuming response is linked to request
        
        # Try to find start time (this is a simplified approach)
        start_time = None
        if self._start_times:
            # Get the most recent start time (not ideal but works for simple cases)
            start_time = max(self._start_times.values())
            # Clean up old entries
            current_time = time.time()
            self._start_times = {k: v for k, v in self._start_times.items() if current_time - v < 60}
        
        if start_time:
            duration = time.time() - start_time
            self.logger.info(f"Request {route_info.path} took {duration:.3f}s")
        
        return response


class ErrorHandlingMiddleware(BaseMiddleware):
    """Middleware for handling errors gracefully"""
    
    def __init__(self, priority: int = 1):
        super().__init__(priority)
    
    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Pass through request"""
        return request
    
    async def process_response(self, response: Any, route_info: RouteInfo, request: Any = None) -> Any:
        """Handle errors in response with route-aware error formatting"""
        try:
            # Check if response is an exception or error
            if isinstance(response, Exception):
                self.logger.error(f"Error in route {route_info.path}: {response}")

                # Use route-aware error response if we have request context
                if request:
                    from ..core.error_detection import ErrorResponseFactory
                    return ErrorResponseFactory.create_error_response(
                        error=response,
                        request=request,
                        route_info=route_info,
                        status_code=500
                    )

                # Fallback to JSON response
                from fastapi.responses import JSONResponse
                return JSONResponse(
                    content={"error": str(response)},
                    status_code=500
                )
            return response
        except Exception as e:
            self.logger.error(f"Error in error handling middleware: {e}")
            return response


class ValidationMiddleware(BaseMiddleware):
    """Middleware for validating requests and responses"""
    
    def __init__(self, priority: int = 80):
        super().__init__(priority)
    
    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Validate incoming request"""
        # Basic validation - can be extended
        if not hasattr(request, 'method'):
            self.logger.warning(f"Request missing method attribute for {route_info.path}")
        
        return request
    
    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Validate outgoing response"""
        # Basic validation - can be extended
        if response is None:
            self.logger.warning(f"Route {route_info.path} returned None response")
        
        return response


class SecurityHeadersMiddleware(BaseMiddleware):
    """Middleware for adding security headers"""
    
    def __init__(self, priority: int = 90):
        super().__init__(priority)
    
    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Pass through request"""
        return request
    
    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Add security headers to response"""
        try:
            # Only add headers to HTTP responses
            if hasattr(response, 'headers'):
                # Add basic security headers
                response.headers.update({
                    'X-Content-Type-Options': 'nosniff',
                    'X-Frame-Options': 'DENY',
                    'X-XSS-Protection': '1; mode=block'
                })
        except Exception as e:
            self.logger.debug(f"Could not add security headers: {e}")
        
        return response
