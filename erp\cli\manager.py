"""
ERP CLI Manager - Main command line interface coordinator
"""
import sys
import argparse
from typing import Dict, Optional

from .base import CommandGroup
from .database import DatabaseCommandGroup
from ..config import config


class ERPCLIManager:
    """Main ERP CLI Manager that coordinates all command groups"""
    
    def __init__(self):
        self.command_groups: Dict[str, CommandGroup] = {}
        self.parser = self._create_parser()
        self._register_command_groups()
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """Create the main argument parser"""
        parser = argparse.ArgumentParser(
            description='ERP System Command Line Interface',
            prog='erp-bin',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  erp-bin init mydb --create
            """
        )
        
        # Global options
        parser.add_argument('--config', '-c', help='Configuration file path')
        parser.add_argument('--db-name', help='Database name')
        parser.add_argument('--addons-path', help='Addons path')
        parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
        parser.add_argument('--version', action='version', version='ERP System 1.0.0')
        
        return parser
    
    def _register_command_groups(self):
        """Register all command groups"""
        # Register command groups
        self.command_groups['database'] = DatabaseCommandGroup()

        
        # Create a parent parser with global options that will be inherited by subcommands
        parent_parser = argparse.ArgumentParser(add_help=False)
        
        # Add subcommands to parser
        subparsers = self.parser.add_subparsers(
            dest='command',
            help='Available commands',
            metavar='COMMAND'
        )
        
        # Add commands from each group with parent parser
        for group_name, group in self.command_groups.items():
            group.add_commands(subparsers, parent_parser)
    
    def run(self, args: Optional[list] = None) -> int:
        """Run the CLI with given arguments"""
        try:
            parsed_args = self.parser.parse_args(args)
            
            # Apply global configuration
            self._apply_global_config(parsed_args)
            
            if not parsed_args.command:
                self.parser.print_help()
                return 1
            
            # Route command to appropriate handler
            return self._handle_command(parsed_args)
            
        except KeyboardInterrupt:
            print("\nOperation cancelled by user")
            return 1
        except Exception as e:
            if getattr(parsed_args, 'verbose', False):
                import traceback
                traceback.print_exc()
            else:
                print(f"Error: {e}")
            return 1
    
    def _apply_global_config(self, args: argparse.Namespace):
        """Apply global configuration options"""
        if args.config:
            config.config_file = args.config
            config._load_config()
        
        if args.db_name:
            config.set('options', 'db_name', args.db_name)
        
        if args.addons_path:
            config.set('options', 'addons_path', args.addons_path)
    
    def _handle_command(self, args: argparse.Namespace) -> int:
        """Handle command execution by routing to appropriate command group"""
        command_name = args.command
        
        # Map commands to their respective groups
        command_mapping = {
            # Database commands
            'init': 'database',
        }
        
        group_name = command_mapping.get(command_name)
        if not group_name:
            print(f"Unknown command: {command_name}")
            return 1
        
        command_group = self.command_groups.get(group_name)
        if not command_group:
            print(f"Command group not found: {group_name}")
            return 1
        
        # Map command names to their handler names in the group
        command_handler_mapping = {
            # Database commands
            'init': 'init',
        }
        
        handler_name = command_handler_mapping.get(command_name, command_name)
        return command_group.handle_command(handler_name, args)
    
    def print_command_help(self, command_name: str):
        """Print help for a specific command"""
        # This could be enhanced to show detailed help for specific commands
        self.parser.print_help()
    
    def list_commands(self) -> Dict[str, list]:
        """List all available commands grouped by category"""
        commands = {}
        for group_name, group in self.command_groups.items():
            commands[group_name] = list(group.commands.keys())
        return commands
    
    def get_command_info(self, command_name: str) -> Optional[dict]:
        """Get information about a specific command"""
        for group_name, group in self.command_groups.items():
            if command_name in group.commands:
                command = group.commands[command_name]
                return {
                    'name': command_name,
                    'group': group_name,
                    'class': command.__class__.__name__,
                    'description': command.__class__.__doc__ or 'No description available'
                }
        return None


def main():
    """Main entry point for the CLI"""
    manager = ERPCLIManager()
    return manager.run()


if __name__ == '__main__':
    sys.exit(main())