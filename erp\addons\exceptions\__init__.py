"""
Addon exceptions

This package contains all exception classes used throughout the addon system.
"""

from .addon_exceptions import (
    AddonError,
    DependencyError,
    CircularDependencyError,
    MissingDependencyError,
    AddonInstallationError,
    AddonUninstallationError,
    AddonUpgradeError,
    AddonManifestError,
    AddonNotFoundError,
    AddonStateError,
    AddonRegistryError,
    AddonHookError,
    AddonLifecycleError,
)

__all__ = [
    'AddonError',
    'DependencyError',
    'CircularDependencyError',
    'MissingDependencyError',
    'AddonInstallationError',
    'AddonUninstallationError',
    'AddonUpgradeError',
    'AddonManifestError',
    'AddonNotFoundError',
    'AddonStateError',
    'AddonRegistryError',
    'AddonHookError',
    'AddonLifecycleError',
]
