"""
FastAPI ASGI Server for ERP system - Modularized version
"""
from fastapi import FastAPI

from .server_config import ServerConfig
from .utils import APIResponse
from .http import setup_http_routes
# Import route modules to ensure systemRoute decorators are executed
from .routes import database, system


class ERPServer:
    """Main ERP server application"""

    def __init__(self):
        """Initialize ERP server"""
        self.server_config = ServerConfig()
        self.app = self._create_app()
        self._setup_server()
        self._setup_routes()
    
    def _create_app(self) -> FastAPI:
        """Create FastAPI application"""
        return self.server_config.create_app()
    
    def _setup_server(self):
        """Setup server configuration"""
        self.server_config.setup_middleware(self.app)
        
        # Set FastAPI app instance in MemoryRegistryManager for route registration
        from .database.memory.registry_manager import MemoryRegistryManager
        MemoryRegistryManager.set_app(self.app)
    
    def _setup_routes(self):
        """Setup system routes"""
        # All routes (system, database) are now registered via systemRoute decorators
        # No need to include routers - routes are registered in SystemRouteRegistry

        # Update system routes
        self._update_system_routes()
    

    
    def _update_system_routes(self):
        """Update system routes with server-specific data"""
        # Update health check
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint - only checks server status, not database connections"""
            try:
                # Only check server health, no database connections during startup
                return APIResponse.success({
                    "status": "healthy",
                    "server": "running",
                    "message": "ERP server is running"
                })
            except Exception as e:
                return APIResponse.error(f"Health check failed: {str(e)}", status_code=503)



# Global server instance (lazy initialization)
_server = None


def create_app():
    """Create and return the FastAPI application"""
    global _server
    if _server is None:
        _server = ERPServer()
    return _server.app
