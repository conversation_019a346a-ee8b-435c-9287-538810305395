"""
Logging Middleware Module
Middleware system for processing log records through configurable pipelines
"""
import logging
import time
import threading
from typing import List, Optional, Dict, Any, Callable
from abc import ABC, abstractmethod
from contextlib import contextmanager
from datetime import datetime


class LoggingMiddleware(ABC):
    """Abstract base class for logging middleware"""
    
    @abstractmethod
    def process(self, record: logging.LogRecord) -> Optional[logging.LogRecord]:
        """
        Process a log record
        
        Args:
            record: The log record to process
            
        Returns:
            The processed record, or None to filter out the record
        """
        pass
    
    def setup(self):
        """Setup middleware (called once during initialization)"""
        pass
    
    def teardown(self):
        """Teardown middleware (called during shutdown)"""
        pass


class ContextMiddleware(LoggingMiddleware):
    """Middleware for adding context information to log records"""
    
    def __init__(self):
        self.context_stack = threading.local()
    
    def process(self, record: logging.LogRecord) -> Optional[logging.LogRecord]:
        """Add context information to log record"""
        # Add default context if not present
        if not hasattr(record, 'user_id'):
            record.user_id = getattr(self.context_stack, 'user_id', 'system')
        
        if not hasattr(record, 'database'):
            record.database = getattr(self.context_stack, 'database', 'default')
        
        if not hasattr(record, 'request_id'):
            record.request_id = getattr(self.context_stack, 'request_id', None)
        
        if not hasattr(record, 'session_id'):
            record.session_id = getattr(self.context_stack, 'session_id', None)
        
        # Add timestamp if not present
        if not hasattr(record, 'timestamp'):
            record.timestamp = datetime.fromtimestamp(record.created).isoformat()
        
        return record
    
    @contextmanager
    def context(self, **kwargs):
        """Context manager for setting context variables"""
        old_values = {}
        
        # Store old values and set new ones
        for key, value in kwargs.items():
            old_values[key] = getattr(self.context_stack, key, None)
            setattr(self.context_stack, key, value)
        
        try:
            yield
        finally:
            # Restore old values
            for key, old_value in old_values.items():
                if old_value is None:
                    if hasattr(self.context_stack, key):
                        delattr(self.context_stack, key)
                else:
                    setattr(self.context_stack, key, old_value)


class PerformanceMiddleware(LoggingMiddleware):
    """Middleware for tracking performance metrics"""
    
    def __init__(self):
        self.request_times = threading.local()
        self.performance_tracker = {}
        self.lock = threading.Lock()
    
    def process(self, record: logging.LogRecord) -> Optional[logging.LogRecord]:
        """Add performance information to log record"""
        # Track function call durations
        if hasattr(record, 'operation'):
            operation = record.operation
            
            if operation.endswith('_start'):
                # Start timing
                base_operation = operation[:-6]  # Remove '_start'
                if not hasattr(self.request_times, 'timings'):
                    self.request_times.timings = {}
                self.request_times.timings[base_operation] = time.perf_counter()
            
            elif operation.endswith('_complete') or operation.endswith('_error'):
                # End timing
                base_operation = operation.replace('_complete', '').replace('_error', '')
                if (hasattr(self.request_times, 'timings') and 
                    base_operation in self.request_times.timings):
                    
                    start_time = self.request_times.timings.pop(base_operation)
                    duration = time.perf_counter() - start_time
                    record.duration = duration
                    
                    # Track performance statistics
                    with self.lock:
                        if base_operation not in self.performance_tracker:
                            self.performance_tracker[base_operation] = {
                                'count': 0,
                                'total_time': 0.0,
                                'min_time': float('inf'),
                                'max_time': 0.0
                            }
                        
                        stats = self.performance_tracker[base_operation]
                        stats['count'] += 1
                        stats['total_time'] += duration
                        stats['min_time'] = min(stats['min_time'], duration)
                        stats['max_time'] = max(stats['max_time'], duration)
                        
                        # Add performance context to record
                        record.avg_duration = stats['total_time'] / stats['count']
                        record.operation_count = stats['count']
        
        return record
    
    def get_performance_stats(self) -> Dict[str, Dict[str, float]]:
        """Get current performance statistics"""
        with self.lock:
            return {
                operation: {
                    'count': stats['count'],
                    'total_time': stats['total_time'],
                    'avg_time': stats['total_time'] / stats['count'] if stats['count'] > 0 else 0.0,
                    'min_time': stats['min_time'] if stats['min_time'] != float('inf') else 0.0,
                    'max_time': stats['max_time']
                }
                for operation, stats in self.performance_tracker.items()
            }


class SecurityMiddleware(LoggingMiddleware):
    """Middleware for handling security-related logging"""
    
    def __init__(self):
        self.sensitive_fields = {
            'password', 'passwd', 'pwd', 'secret', 'token', 'key', 'auth',
            'authorization', 'credential', 'private', 'confidential'
        }
        self.security_events = threading.local()
    
    def process(self, record: logging.LogRecord) -> Optional[logging.LogRecord]:
        """Process security-related log records"""
        # Sanitize sensitive information
        message = record.getMessage()
        
        # Check for sensitive information in message
        lower_message = message.lower()
        for sensitive_field in self.sensitive_fields:
            if sensitive_field in lower_message:
                # Mark as potentially sensitive
                record.contains_sensitive = True
                break
        else:
            record.contains_sensitive = False
        
        # Add security context
        if hasattr(record, 'operation'):
            operation = record.operation
            
            # Track security events
            if any(sec_term in operation.lower() for sec_term in 
                   ['auth', 'login', 'logout', 'permission', 'access', 'security']):
                record.security_event = True
                
                # Track security event statistics
                if not hasattr(self.security_events, 'events'):
                    self.security_events.events = {}
                
                if operation not in self.security_events.events:
                    self.security_events.events[operation] = 0
                self.security_events.events[operation] += 1
                
                record.security_event_count = self.security_events.events[operation]
        
        return record
    
    def sanitize_message(self, message: str) -> str:
        """Sanitize sensitive information from message"""
        # Simple sanitization - replace sensitive patterns
        import re
        
        # Pattern for key=value pairs with sensitive keys
        for field in self.sensitive_fields:
            pattern = rf'\b{field}[=:]\s*[^\s,;)}}]+\b'
            message = re.sub(pattern, f'{field}=***', message, flags=re.IGNORECASE)
        
        return message


class FilterMiddleware(LoggingMiddleware):
    """Middleware for filtering log records based on conditions"""
    
    def __init__(self, filter_func: Optional[Callable[[logging.LogRecord], bool]] = None):
        self.filter_func = filter_func or (lambda record: True)
        self.filtered_count = 0
        self.total_count = 0
    
    def process(self, record: logging.LogRecord) -> Optional[logging.LogRecord]:
        """Filter log records based on conditions"""
        self.total_count += 1
        
        if self.filter_func(record):
            return record
        else:
            self.filtered_count += 1
            return None
    
    def get_filter_stats(self) -> Dict[str, int]:
        """Get filtering statistics"""
        return {
            'total_records': self.total_count,
            'filtered_records': self.filtered_count,
            'passed_records': self.total_count - self.filtered_count
        }


class LoggingPipeline:
    """Pipeline for processing log records through middleware"""
    
    def __init__(self):
        self.middleware: List[LoggingMiddleware] = []
        self.enabled = True
    
    def add_middleware(self, middleware: LoggingMiddleware):
        """Add middleware to the pipeline"""
        self.middleware.append(middleware)
        middleware.setup()
    
    def remove_middleware(self, middleware: LoggingMiddleware):
        """Remove middleware from the pipeline"""
        if middleware in self.middleware:
            self.middleware.remove(middleware)
            middleware.teardown()
    
    def process_record(self, record: logging.LogRecord) -> Optional[logging.LogRecord]:
        """Process a log record through the middleware pipeline"""
        if not self.enabled:
            return record
        
        current_record = record
        
        for middleware in self.middleware:
            try:
                current_record = middleware.process(current_record)
                if current_record is None:
                    # Record was filtered out
                    return None
            except Exception as e:
                # Don't let middleware errors break logging
                # Log the error if possible
                try:
                    error_record = logging.LogRecord(
                        name='erp.logging.middleware',
                        level=logging.ERROR,
                        pathname='',
                        lineno=0,
                        msg=f"Middleware error in {middleware.__class__.__name__}: {e}",
                        args=(),
                        exc_info=None
                    )
                    # Continue with original record
                except Exception:
                    pass
        
        return current_record
    
    def enable(self):
        """Enable the pipeline"""
        self.enabled = True
    
    def disable(self):
        """Disable the pipeline"""
        self.enabled = False
    
    def shutdown(self):
        """Shutdown the pipeline and all middleware"""
        for middleware in self.middleware:
            try:
                middleware.teardown()
            except Exception:
                pass
        self.middleware.clear()


# Global pipeline instance
_global_pipeline: Optional[LoggingPipeline] = None


def get_global_pipeline() -> LoggingPipeline:
    """Get the global logging pipeline"""
    global _global_pipeline
    if _global_pipeline is None:
        _global_pipeline = LoggingPipeline()
        # Add default middleware
        _global_pipeline.add_middleware(ContextMiddleware())
        _global_pipeline.add_middleware(PerformanceMiddleware())
        _global_pipeline.add_middleware(SecurityMiddleware())
    return _global_pipeline


def process_log_record(record: logging.LogRecord) -> Optional[logging.LogRecord]:
    """Process a log record through the global pipeline"""
    return get_global_pipeline().process_record(record)
