"""
Test suite for database registry functionality

This module tests:
- Database registry operations and management
- Database manager creation and caching
- Current database tracking
- Database lifecycle management
"""
import pytest
from unittest.mock import patch

from erp.database import DatabaseRegistry


class TestDatabaseRegistry:
    """Test DatabaseRegistry functionality"""
    
    @pytest.mark.asyncio
    async def test_get_database_creates_new_manager(self, mock_database_manager):
        """Test that get_database creates a new manager for unknown databases"""
        # Clear any existing databases
        DatabaseRegistry._databases.clear()
        
        with patch('erp.database.registry.database_registry.DatabaseManager') as mock_manager_class:
            mock_manager_class.return_value = mock_database_manager
            
            result = await DatabaseRegistry.get_database("test_db")
            
            assert result == mock_database_manager
            assert "test_db" in DatabaseRegistry._databases
            mock_database_manager.create_pool.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_database_returns_existing_manager(self, mock_database_manager):
        """Test that get_database returns existing manager for known databases"""
        # Setup existing database
        DatabaseRegistry._databases["existing_db"] = mock_database_manager
        
        result = await DatabaseRegistry.get_database("existing_db")
        
        assert result == mock_database_manager
        # Should not call create_pool again
        mock_database_manager.create_pool.assert_not_called()
    
    def test_set_current_database(self):
        """Test setting current database"""
        DatabaseRegistry.set_current_database("current_test_db")
        assert DatabaseRegistry._current_db == "current_test_db"
    
    @pytest.mark.asyncio
    async def test_get_current_database_with_set_db(self, mock_database_manager):
        """Test getting current database when one is set"""
        DatabaseRegistry._databases["current_db"] = mock_database_manager
        DatabaseRegistry._current_db = "current_db"
        
        result = await DatabaseRegistry.get_current_database()
        assert result == mock_database_manager
    
    @pytest.mark.asyncio
    async def test_get_current_database_none_set(self):
        """Test getting current database when none is set"""
        DatabaseRegistry._current_db = None
        result = await DatabaseRegistry.get_current_database()
        assert result is None
