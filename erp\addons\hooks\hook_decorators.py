"""
Hook decorators

This module provides decorator functions for registering hooks.
"""
import inspect
from typing import Callable, Optional, Any

from .hook_types import HookType
from .hook_context import Hook<PERSON>ontext
from .hook_registry import get_hook_registry


def _get_addon_name_from_module(func: Callable[[HookContext], Any], addon_name: Optional[str]) -> str:
    """Helper function to determine addon name from module or explicit parameter"""
    if addon_name is not None:
        return addon_name
    
    # Try to infer addon name from module
    module = inspect.getmodule(func)
    if module and hasattr(module, '__name__'):
        parts = module.__name__.split('.')
        if len(parts) >= 3 and parts[0] == 'erp' and parts[1] == 'addons':
            return parts[2]
    
    raise ValueError("Could not determine addon name. Please specify explicitly.")


def pre_install_hook(addon_name: Optional[str] = None, priority: int = 50) -> Callable[[Callable[[HookContext], Any]], Callable[[HookContext], Any]]:
    """Decorator for pre-install hooks"""
    def decorator(func: Callable[[HookContext], Any]) -> Callable[[HookContext], Any]:
        resolved_addon_name = _get_addon_name_from_module(func, addon_name)
        get_hook_registry().register_hook(func, HookType.PRE_INSTALL, resolved_addon_name, priority)
        return func
    return decorator


def post_install_hook(addon_name: Optional[str] = None, priority: int = 50) -> Callable[[Callable[[HookContext], Any]], Callable[[HookContext], Any]]:
    """Decorator for post-install hooks"""
    def decorator(func: Callable[[HookContext], Any]) -> Callable[[HookContext], Any]:
        resolved_addon_name = _get_addon_name_from_module(func, addon_name)
        get_hook_registry().register_hook(func, HookType.POST_INSTALL, resolved_addon_name, priority)
        return func
    return decorator


def pre_uninstall_hook(addon_name: Optional[str] = None, priority: int = 50) -> Callable[[Callable[[HookContext], Any]], Callable[[HookContext], Any]]:
    """Decorator for pre-uninstall hooks"""
    def decorator(func: Callable[[HookContext], Any]) -> Callable[[HookContext], Any]:
        resolved_addon_name = _get_addon_name_from_module(func, addon_name)
        get_hook_registry().register_hook(func, HookType.PRE_UNINSTALL, resolved_addon_name, priority)
        return func
    return decorator


def post_uninstall_hook(addon_name: Optional[str] = None, priority: int = 50) -> Callable[[Callable[[HookContext], Any]], Callable[[HookContext], Any]]:
    """Decorator for post-uninstall hooks"""
    def decorator(func: Callable[[HookContext], Any]) -> Callable[[HookContext], Any]:
        resolved_addon_name = _get_addon_name_from_module(func, addon_name)
        get_hook_registry().register_hook(func, HookType.POST_UNINSTALL, resolved_addon_name, priority)
        return func
    return decorator


def pre_upgrade_hook(addon_name: Optional[str] = None, priority: int = 50) -> Callable[[Callable[[HookContext], Any]], Callable[[HookContext], Any]]:
    """Decorator for pre-upgrade hooks"""
    def decorator(func: Callable[[HookContext], Any]) -> Callable[[HookContext], Any]:
        resolved_addon_name = _get_addon_name_from_module(func, addon_name)
        get_hook_registry().register_hook(func, HookType.PRE_UPGRADE, resolved_addon_name, priority)
        return func
    return decorator


def post_upgrade_hook(addon_name: Optional[str] = None, priority: int = 50) -> Callable[[Callable[[HookContext], Any]], Callable[[HookContext], Any]]:
    """Decorator for post-upgrade hooks"""
    def decorator(func: Callable[[HookContext], Any]) -> Callable[[HookContext], Any]:
        resolved_addon_name = _get_addon_name_from_module(func, addon_name)
        get_hook_registry().register_hook(func, HookType.POST_UPGRADE, resolved_addon_name, priority)
        return func
    return decorator
