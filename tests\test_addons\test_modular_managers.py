"""
Test suite for modular addon managers

This module tests:
- AddonManager functionality
- DependencyManager functionality
- AddonStateManager functionality
- Manager integration and workflow
- State management and transitions
"""
import pytest
from unittest.mock import MagicMock, AsyncMock, patch
from datetime import datetime

from erp.addons.managers import (
    AddonManager,
    DependencyManager,
    AddonStateManager,
    AddonState,
    AddonInfo
)


class TestAddonState:
    """Test AddonState enumeration"""
    
    def test_addon_state_values(self):
        """Test that all addon states are defined"""
        assert AddonState.UNINSTALLED
        assert AddonState.INSTALLED
        assert AddonState.TO_INSTALL
        assert AddonState.TO_UPGRADE
        assert AddonState.TO_REMOVE
    
    def test_addon_state_string_representation(self):
        """Test addon state string representation"""
        assert str(AddonState.INSTALLED) in ["AddonState.INSTALLED", "installed"]
        assert str(AddonState.UNINSTALLED) in ["AddonState.UNINSTALLED", "uninstalled"]


class TestAddonInfo:
    """Test AddonInfo data class"""
    
    def test_addon_info_creation(self):
        """Test AddonInfo creation"""
        info = AddonInfo(
            name="test_addon",
            version="1.0.0",
            state=AddonState.INSTALLED,
            path="/path/to/addon"
        )
        
        assert info.name == "test_addon"
        assert info.version == "1.0.0"
        assert info.state == AddonState.INSTALLED
        assert info.path == "/path/to/addon"
    
    def test_addon_info_with_optional_fields(self):
        """Test AddonInfo with optional fields"""
        install_date = datetime.now()
        
        info = AddonInfo(
            name="test_addon",
            version="1.0.0",
            state=AddonState.INSTALLED,
            path="/path/to/addon",
            dependencies=["base", "web"],
            install_date=install_date,
            description="Test addon"
        )
        
        assert info.dependencies == ["base", "web"]
        assert info.install_date == install_date
        assert info.description == "Test addon"


class TestAddonStateManager:
    """Test AddonStateManager functionality"""
    
    def test_addon_state_manager_creation(self):
        """Test AddonStateManager creation"""
        mock_env = MagicMock()
        manager = AddonStateManager(mock_env)
        assert manager.env == mock_env
    
    @pytest.mark.asyncio
    async def test_get_addon_state(self):
        """Test getting addon state"""
        mock_env = MagicMock()
        manager = AddonStateManager(mock_env)
        
        with patch.object(manager, 'get_addon_state', return_value=AddonState.INSTALLED) as mock_get:
            state = await manager.get_addon_state("test_addon")
            assert state == AddonState.INSTALLED
            mock_get.assert_called_once_with("test_addon")
    
    @pytest.mark.asyncio
    async def test_set_addon_state(self):
        """Test setting addon state"""
        mock_env = MagicMock()
        manager = AddonStateManager(mock_env)
        
        with patch.object(manager, 'set_addon_state', return_value=True) as mock_set:
            result = await manager.set_addon_state("test_addon", AddonState.INSTALLED)
            assert result is True
            mock_set.assert_called_once_with("test_addon", AddonState.INSTALLED)
    
    @pytest.mark.asyncio
    async def test_get_addon_info(self):
        """Test getting addon info"""
        mock_env = MagicMock()
        manager = AddonStateManager(mock_env)
        
        expected_info = AddonInfo(
            name="test_addon",
            version="1.0.0",
            state=AddonState.INSTALLED,
            path="/path/to/addon"
        )
        
        with patch.object(manager, 'get_addon_info', return_value=expected_info) as mock_get:
            info = await manager.get_addon_info("test_addon")
            assert info.name == "test_addon"
            assert info.state == AddonState.INSTALLED
            mock_get.assert_called_once_with("test_addon")
    
    @pytest.mark.asyncio
    async def test_list_addons_by_state(self):
        """Test listing addons by state"""
        mock_env = MagicMock()
        manager = AddonStateManager(mock_env)
        
        expected_addons = ["addon1", "addon2", "addon3"]
        
        with patch.object(manager, 'list_addons_by_state', return_value=expected_addons) as mock_list:
            addons = await manager.list_addons_by_state(AddonState.INSTALLED)
            assert addons == expected_addons
            mock_list.assert_called_once_with(AddonState.INSTALLED)


class TestDependencyManager:
    """Test DependencyManager functionality"""
    
    def test_dependency_manager_creation(self):
        """Test DependencyManager creation"""
        mock_env = MagicMock()
        manager = DependencyManager(mock_env)
        assert manager.env == mock_env
    
    @pytest.mark.asyncio
    async def test_resolve_dependencies(self):
        """Test dependency resolution"""
        mock_env = MagicMock()
        manager = DependencyManager(mock_env)
        
        dependencies = ["base", "web"]
        expected_order = ["base", "web", "test_addon"]
        
        with patch.object(manager, 'resolve_dependencies', return_value=expected_order) as mock_resolve:
            order = await manager.resolve_dependencies("test_addon", dependencies)
            assert order == expected_order
            mock_resolve.assert_called_once_with("test_addon", dependencies)
    
    @pytest.mark.asyncio
    async def test_check_circular_dependencies(self):
        """Test circular dependency detection"""
        mock_env = MagicMock()
        manager = DependencyManager(mock_env)
        
        with patch.object(manager, 'check_circular_dependencies', return_value=False) as mock_check:
            has_circular = await manager.check_circular_dependencies("test_addon", ["base"])
            assert has_circular is False
            mock_check.assert_called_once_with("test_addon", ["base"])
    
    @pytest.mark.asyncio
    async def test_get_missing_dependencies(self):
        """Test missing dependency detection"""
        mock_env = MagicMock()
        manager = DependencyManager(mock_env)
        
        dependencies = ["base", "web", "missing_addon"]
        expected_missing = ["missing_addon"]
        
        with patch.object(manager, 'get_missing_dependencies', return_value=expected_missing) as mock_missing:
            missing = await manager.get_missing_dependencies(dependencies)
            assert missing == expected_missing
            mock_missing.assert_called_once_with(dependencies)
    
    @pytest.mark.asyncio
    async def test_get_installation_order(self):
        """Test installation order calculation"""
        mock_env = MagicMock()
        manager = DependencyManager(mock_env)
        
        addons = ["addon1", "addon2", "addon3"]
        expected_order = ["addon1", "addon2", "addon3"]
        
        with patch.object(manager, 'get_installation_order', return_value=expected_order) as mock_order:
            order = await manager.get_installation_order(addons)
            assert order == expected_order
            mock_order.assert_called_once_with(addons)


class TestAddonManager:
    """Test AddonManager functionality"""
    
    def test_addon_manager_creation(self):
        """Test AddonManager creation"""
        mock_env = MagicMock()
        manager = AddonManager(mock_env)
        assert manager.env == mock_env
    
    @pytest.mark.asyncio
    async def test_install_addon(self):
        """Test addon installation"""
        mock_env = MagicMock()
        manager = AddonManager(mock_env)
        
        with patch.object(manager, 'install_addon', return_value={"success": True, "message": "Installed"}) as mock_install:
            result = await manager.install_addon("test_addon", "/path/to/addon")
            assert result["success"] is True
            mock_install.assert_called_once_with("test_addon", "/path/to/addon")
    
    @pytest.mark.asyncio
    async def test_uninstall_addon(self):
        """Test addon uninstallation"""
        mock_env = MagicMock()
        manager = AddonManager(mock_env)
        
        with patch.object(manager, 'uninstall_addon', return_value={"success": True, "message": "Uninstalled"}) as mock_uninstall:
            result = await manager.uninstall_addon("test_addon")
            assert result["success"] is True
            mock_uninstall.assert_called_once_with("test_addon")
    
    @pytest.mark.asyncio
    async def test_upgrade_addon(self):
        """Test addon upgrade"""
        mock_env = MagicMock()
        manager = AddonManager(mock_env)
        
        with patch.object(manager, 'upgrade_addon', return_value={"success": True, "message": "Upgraded"}) as mock_upgrade:
            result = await manager.upgrade_addon("test_addon")
            assert result["success"] is True
            mock_upgrade.assert_called_once_with("test_addon")
    
    @pytest.mark.asyncio
    async def test_list_available_addons(self):
        """Test listing available addons"""
        mock_env = MagicMock()
        manager = AddonManager(mock_env)
        
        expected_addons = ["addon1", "addon2", "addon3"]
        
        with patch.object(manager, 'list_available_addons', return_value=expected_addons) as mock_list:
            addons = await manager.list_available_addons()
            assert addons == expected_addons
            mock_list.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_addon_status(self):
        """Test getting addon status"""
        mock_env = MagicMock()
        manager = AddonManager(mock_env)
        
        expected_status = {
            "name": "test_addon",
            "state": "installed",
            "version": "1.0.0"
        }
        
        with patch.object(manager, 'get_addon_status', return_value=expected_status) as mock_status:
            status = await manager.get_addon_status("test_addon")
            assert status == expected_status
            mock_status.assert_called_once_with("test_addon")


class TestManagerIntegration:
    """Test manager integration and workflow"""
    
    @pytest.mark.asyncio
    async def test_manager_workflow(self):
        """Test complete manager workflow"""
        mock_env = MagicMock()
        
        # Create managers
        addon_manager = AddonManager(mock_env)
        dependency_manager = DependencyManager(mock_env)
        state_manager = AddonStateManager(mock_env)
        
        # Mock workflow steps
        with patch.object(dependency_manager, 'resolve_dependencies', return_value=["base", "test_addon"]), \
             patch.object(dependency_manager, 'check_circular_dependencies', return_value=False), \
             patch.object(state_manager, 'set_addon_state', return_value=True), \
             patch.object(addon_manager, 'install_addon', return_value={"success": True}):
            
            # Simulate workflow
            deps_order = await dependency_manager.resolve_dependencies("test_addon", ["base"])
            assert "test_addon" in deps_order
            
            has_circular = await dependency_manager.check_circular_dependencies("test_addon", ["base"])
            assert has_circular is False
            
            state_set = await state_manager.set_addon_state("test_addon", AddonState.TO_INSTALL)
            assert state_set is True
            
            install_result = await addon_manager.install_addon("test_addon", "/path")
            assert install_result["success"] is True
    
    def test_all_manager_components_importable(self):
        """Test that all manager components can be imported"""
        from erp.addons.managers import __all__
        
        expected_components = [
            'AddonState',
            'AddonInfo',
            'AddonStateManager',
            'DependencyManager',
            'AddonManager',
        ]
        
        for component in expected_components:
            assert component in __all__
