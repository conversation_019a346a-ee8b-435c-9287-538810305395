"""
Legacy compatibility module for error detection
This module re-exports functionality from the new modular error handling system
"""

# Re-export the main functionality from the new modules
from .error_responses import ErrorResponseFactory, create_appropriate_error_response
from .route_detection import RouteTypeDetector, detect_route_type_from_request

# For backward compatibility
__all__ = [
    'ErrorResponseFactory',
    'RouteTypeDetector',
    'create_appropriate_error_response',
    'detect_route_type_from_request'
]
