"""
Middleware pipeline system for HTTP routes
Provides composable middleware components for request/response processing
"""

from .pipeline import MiddlewarePipeline, get_middleware_pipeline
from .base import BaseMiddleware
from .cors import CorsMiddleware
from .request import RequestProcessingMiddleware
from .response import ResponseProcessingMiddleware

# Note: AuthMiddleware is imported from erp.http.auth to avoid circular imports

__all__ = [
    'MiddlewarePipeline',
    'get_middleware_pipeline',
    'BaseMiddleware',
    'CorsMiddleware',
    'RequestProcessingMiddleware',
    'ResponseProcessingMiddleware',
]
