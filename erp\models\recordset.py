"""
RecordSet implementation for Odoo-style ORM
Provides a container for model instances with convenient API
"""
from typing import List, Any, Dict, Union, Iterator, Optional
from datetime import datetime
from ..utils import DomainFilter


class RecordSet:
    """
    An ordered collection of records of a given model.
    It mimics the behavior of a list and provides Odoo-like
    recordset capabilities.
    """
    
    def __init__(self, model_class, records: List = None):
        """
        Initialize RecordSet
        
        Args:
            model_class: The model class this recordset belongs to
            records: List of model instances
        """
        self._model = model_class
        self._records = records or []
        self._ids = [r.id for r in self._records if hasattr(r, 'id') and r.id]
    
    def __len__(self) -> int:
        """Return number of records in the set"""
        return len(self._records)
    
    def __iter__(self) -> Iterator:
        """Iterate over records"""
        return iter(self._records)
    
    def __getitem__(self, index: Union[int, slice]):
        """Get record(s) by index or slice"""
        if isinstance(index, slice):
            # Return a new RecordSet with sliced records
            return RecordSet(self._model, self._records[index])
        return self._records[index]
    
    def __bool__(self) -> bool:
        """Return True if recordset is not empty"""
        return len(self._records) > 0
    
    def __repr__(self) -> str:
        """String representation of the recordset"""
        if hasattr(self._model, '_name'):
            model_name = self._model._name
        else:
            model_name = self._model.__name__
        return f"{model_name}.browse({self._ids})"
    
    def __getattr__(self, name: str) -> Any:
        """
        Allows direct attribute access if the recordset contains
        exactly one record.
        """
        if len(self._records) == 1:
            return getattr(self._records[0], name)
        elif len(self._records) == 0:
            raise AttributeError(
                f"Empty RecordSet has no attribute '{name}'"
            )
        else:
            raise AttributeError(
                f"RecordSet with {len(self._records)} records has no attribute '{name}'. "
                "Try to iterate over the records or use mapped()."
            )
    
    def __setattr__(self, name: str, value: Any) -> None:
        """
        Set attribute on all records in the set or on the recordset itself
        """
        # Handle internal attributes
        if name.startswith('_') or name in ('mapped', 'write', 'unlink', 'exists', 'filtered', 'sorted'):
            super().__setattr__(name, value)
            return
        
        # If we have exactly one record, set the attribute on it
        if len(self._records) == 1:
            setattr(self._records[0], name, value)
        elif len(self._records) == 0:
            raise AttributeError(f"Cannot set attribute '{name}' on empty RecordSet")
        else:
            # Set attribute on all records
            for record in self._records:
                if hasattr(record, name):
                    setattr(record, name, value)
                else:
                    raise AttributeError(f"Record has no attribute '{name}'")
    
    def mapped(self, name: str) -> List[Any]:
        """
        Return a list of attribute values for each record in the set.
        
        Args:
            name: Attribute name to map
            
        Returns:
            List of attribute values
        """
        return [getattr(r, name) for r in self._records if hasattr(r, name)]
    
    def filtered(self, func_or_domain) -> 'RecordSet':
        """
        Filter records based on a function or domain
        
        Args:
            func_or_domain: Function that takes a record and returns bool,
                          or a domain list for filtering
                          
        Returns:
            New RecordSet with filtered records
        """
        if callable(func_or_domain):
            filtered_records = [r for r in self._records if func_or_domain(r)]
        else:
            # Use DomainFilter for domain-based filtering
            filtered_records = DomainFilter.filter_records(self._records, func_or_domain)
        
        return RecordSet(self._model, filtered_records)
    
    def sorted(self, key=None, reverse=False) -> 'RecordSet':
        """
        Sort records based on a key function
        
        Args:
            key: Function to extract comparison key from each record
            reverse: If True, sort in descending order
            
        Returns:
            New RecordSet with sorted records
        """
        if key is None:
            # Default sort by id if available
            key = lambda r: getattr(r, 'id', '')
        
        sorted_records = sorted(self._records, key=key, reverse=reverse)
        return RecordSet(self._model, sorted_records)
    
    def exists(self) -> 'RecordSet':
        """
        Return a recordset with only existing records
        For now, just return self (all records are assumed to exist)
        """
        return self
    
    async def write(self, vals: Dict[str, Any]) -> bool:
        """
        Write values to all records in the set.
        
        Args:
            vals: Dictionary of field values to update
            
        Returns:
            True if successful
        """
        for record in self._records:
            await record.write(vals)
        return True
    
    async def unlink(self) -> bool:
        """
        Delete all records in the set.
        
        Returns:
            True if successful
        """
        for record in self._records:
            await record.unlink()
        return True
    
    def ids(self) -> List[str]:
        """
        Return list of record IDs
        
        Returns:
            List of record IDs
        """
        return self._ids.copy()
    
    def ensure_one(self):
        """
        Ensure the recordset contains exactly one record
        
        Returns:
            The single record
            
        Raises:
            ValueError: If recordset doesn't contain exactly one record
        """
        if len(self._records) == 0:
            raise ValueError("Expected singleton: empty recordset")
        elif len(self._records) > 1:
            raise ValueError(f"Expected singleton: recordset contains {len(self._records)} records")
        return self._records[0]
    
    def with_context(self, **context) -> 'RecordSet':
        """
        Return a new recordset with updated context
        For now, just return self (context not implemented yet)
        """
        # TODO: Implement context support
        return self
    
    def browse(self, ids: Union[str, List[str]]) -> 'RecordSet':
        """
        Browse records by IDs and return a new RecordSet
        
        Args:
            ids: Single ID or list of IDs
            
        Returns:
            New RecordSet with browsed records
        """
        # This would typically call the model's browse method
        # For now, filter existing records by the given IDs
        if isinstance(ids, str):
            ids = [ids]
        
        filtered_records = [r for r in self._records if getattr(r, 'id', None) in ids]
        return RecordSet(self._model, filtered_records)
