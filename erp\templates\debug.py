"""
Template Debugging - Development tools and debugging features
"""
import json
import traceback
import time
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from .exceptions import TemplateError


@dataclass
class DebugInfo:
    """Debug information for template rendering"""
    template_name: str
    element_tag: str
    element_line: Optional[int] = None
    directive: Optional[str] = None
    expression: Optional[str] = None
    context_vars: Optional[Dict[str, Any]] = None
    render_time: Optional[float] = None
    error: Optional[str] = None


class TemplateDebugger:
    """Template debugging and development tools"""
    
    def __init__(self, enabled: bool = False):
        self.enabled = enabled
        self.debug_log: List[DebugInfo] = []
        self.context_snapshots: Dict[str, Dict[str, Any]] = {}
        self.render_stack: List[str] = []
        self.max_log_size = 1000
    
    def log_debug(self, info: DebugInfo) -> None:
        """Log debug information"""
        if not self.enabled:
            return
        
        self.debug_log.append(info)
        
        # Keep log size manageable
        if len(self.debug_log) > self.max_log_size:
            self.debug_log = self.debug_log[-self.max_log_size//2:]
    
    def enter_template(self, template_name: str) -> None:
        """Enter template rendering"""
        if self.enabled:
            self.render_stack.append(template_name)
    
    def exit_template(self, template_name: str) -> None:
        """Exit template rendering"""
        if self.enabled and self.render_stack and self.render_stack[-1] == template_name:
            self.render_stack.pop()
    
    def snapshot_context(self, snapshot_id: str, context: Dict[str, Any]) -> None:
        """Take a snapshot of the current context"""
        if not self.enabled:
            return
        
        # Create a serializable snapshot
        snapshot = {}
        for key, value in context.items():
            try:
                # Try to serialize the value
                json.dumps(value)
                snapshot[key] = value
            except (TypeError, ValueError):
                # For non-serializable values, store type and representation
                snapshot[key] = {
                    '__debug_type__': type(value).__name__,
                    '__debug_repr__': repr(value)[:200]  # Limit length
                }
        
        self.context_snapshots[snapshot_id] = snapshot
    
    def get_debug_log(self, template_name: Optional[str] = None) -> List[DebugInfo]:
        """Get debug log, optionally filtered by template name"""
        if template_name:
            return [info for info in self.debug_log if info.template_name == template_name]
        return self.debug_log.copy()
    
    def get_context_snapshot(self, snapshot_id: str) -> Optional[Dict[str, Any]]:
        """Get a context snapshot"""
        return self.context_snapshots.get(snapshot_id)
    
    def clear_log(self) -> None:
        """Clear debug log"""
        self.debug_log.clear()
        self.context_snapshots.clear()
        self.render_stack.clear()
    
    def get_render_stack(self) -> List[str]:
        """Get current render stack"""
        return self.render_stack.copy()
    
    def format_debug_report(self) -> str:
        """Format a comprehensive debug report"""
        if not self.enabled:
            return "Debugging is disabled"
        
        report = ["=== Template Debug Report ===\n"]
        
        # Summary
        report.append(f"Total debug entries: {len(self.debug_log)}")
        report.append(f"Context snapshots: {len(self.context_snapshots)}")
        report.append(f"Current render stack: {' -> '.join(self.render_stack)}")
        report.append("")
        
        # Errors
        errors = [info for info in self.debug_log if info.error]
        if errors:
            report.append("=== Errors ===")
            for error_info in errors[-10:]:  # Last 10 errors
                report.append(f"Template: {error_info.template_name}")
                report.append(f"Element: {error_info.element_tag}")
                if error_info.directive:
                    report.append(f"Directive: {error_info.directive}")
                if error_info.expression:
                    report.append(f"Expression: {error_info.expression}")
                report.append(f"Error: {error_info.error}")
                report.append("")
        
        # Performance issues
        slow_renders = [info for info in self.debug_log 
                       if info.render_time and info.render_time > 0.1]
        if slow_renders:
            report.append("=== Slow Renders (>100ms) ===")
            for slow_info in sorted(slow_renders, key=lambda x: x.render_time, reverse=True)[:5]:
                report.append(f"Template: {slow_info.template_name} ({slow_info.render_time:.3f}s)")
                report.append(f"Element: {slow_info.element_tag}")
                if slow_info.directive:
                    report.append(f"Directive: {slow_info.directive}")
                report.append("")
        
        return "\n".join(report)


class DebugContext:
    """Context manager for debugging template operations"""
    
    def __init__(self, debugger: TemplateDebugger, template_name: str, 
                 element_tag: str, directive: Optional[str] = None):
        self.debugger = debugger
        self.template_name = template_name
        self.element_tag = element_tag
        self.directive = directive
        self.start_time = None
    
    def __enter__(self):
        if self.debugger.enabled:
            self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if not self.debugger.enabled:
            return
        
        render_time = time.time() - self.start_time if self.start_time else None
        error = str(exc_val) if exc_val else None
        
        debug_info = DebugInfo(
            template_name=self.template_name,
            element_tag=self.element_tag,
            directive=self.directive,
            render_time=render_time,
            error=error
        )
        
        self.debugger.log_debug(debug_info)


def format_template_error(error: Exception, template_name: str, 
                         element_info: Optional[Dict[str, Any]] = None) -> str:
    """Format a template error with debugging information"""
    lines = [
        f"Template Error in '{template_name}':",
        f"Error Type: {type(error).__name__}",
        f"Error Message: {str(error)}",
        ""
    ]
    
    if element_info:
        lines.extend([
            "Element Information:",
            f"  Tag: {element_info.get('tag', 'unknown')}",
            f"  Attributes: {element_info.get('attributes', {})}",
            ""
        ])
    
    # Add traceback
    lines.extend([
        "Traceback:",
        traceback.format_exc(),
        ""
    ])
    
    return "\n".join(lines)


def inspect_context(context: Dict[str, Any], max_depth: int = 3) -> Dict[str, Any]:
    """Inspect context variables for debugging"""
    def inspect_value(value, depth=0):
        if depth >= max_depth:
            return f"<max_depth_reached: {type(value).__name__}>"
        
        if isinstance(value, dict):
            if depth < max_depth - 1:
                return {k: inspect_value(v, depth + 1) for k, v in list(value.items())[:10]}
            else:
                return f"<dict with {len(value)} items>"
        elif isinstance(value, (list, tuple)):
            if depth < max_depth - 1:
                return [inspect_value(item, depth + 1) for item in value[:5]]
            else:
                return f"<{type(value).__name__} with {len(value)} items>"
        elif isinstance(value, str):
            return value[:100] + "..." if len(value) > 100 else value
        elif isinstance(value, (int, float, bool, type(None))):
            return value
        else:
            return f"<{type(value).__name__}: {repr(value)[:50]}>"
    
    return {key: inspect_value(value) for key, value in context.items()}


def create_debug_template(template_name: str, context: Dict[str, Any], 
                         debug_info: List[DebugInfo]) -> str:
    """Create a debug template showing context and debug information"""
    context_json = json.dumps(inspect_context(context), indent=2, default=str)
    
    debug_entries = []
    for info in debug_info[-20:]:  # Last 20 entries
        entry = {
            'template': info.template_name,
            'element': info.element_tag,
            'directive': info.directive,
            'expression': info.expression,
            'render_time': f"{info.render_time:.4f}s" if info.render_time else None,
            'error': info.error
        }
        debug_entries.append(entry)
    
    debug_json = json.dumps(debug_entries, indent=2, default=str)
    
    return f'''<!DOCTYPE html>
<html>
<head>
    <title>Debug: {template_name}</title>
    <style>
        body {{ font-family: monospace; margin: 20px; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ccc; }}
        .section h2 {{ margin-top: 0; color: #333; }}
        pre {{ background: #f5f5f5; padding: 10px; overflow: auto; max-height: 400px; }}
        .error {{ color: red; }}
        .warning {{ color: orange; }}
        .info {{ color: blue; }}
    </style>
</head>
<body>
    <h1>Template Debug Information: {template_name}</h1>
    
    <div class="section">
        <h2>Context Variables</h2>
        <pre>{context_json}</pre>
    </div>
    
    <div class="section">
        <h2>Debug Log</h2>
        <pre>{debug_json}</pre>
    </div>
    
    <div class="section">
        <h2>Performance Summary</h2>
        <p>Total debug entries: {len(debug_info)}</p>
        <p>Errors: {len([info for info in debug_info if info.error])}</p>
        <p>Slow renders: {len([info for info in debug_info if info.render_time and info.render_time > 0.1])}</p>
    </div>
</body>
</html>'''


# Global debugger instance
template_debugger = TemplateDebugger()


def get_template_debugger() -> TemplateDebugger:
    """Get the global template debugger instance"""
    return template_debugger


def enable_debugging(enabled: bool = True) -> None:
    """Enable or disable template debugging globally"""
    template_debugger.enabled = enabled


def is_debugging_enabled() -> bool:
    """Check if debugging is enabled"""
    return template_debugger.enabled
